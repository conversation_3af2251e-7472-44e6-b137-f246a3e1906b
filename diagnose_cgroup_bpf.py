#!/usr/bin/env python3
"""
诊断cgroup BPF加载问题

分析为什么cgroup BPF程序无法加载
"""

import os
import sys
import subprocess
from bcc import BPF

def check_system_support():
    """检查系统对BPF和cgroup的支持"""
    print("=== 系统支持检查 ===")
    
    # 检查内核版本
    kernel_version = subprocess.check_output(['uname', '-r']).decode().strip()
    print(f"内核版本: {kernel_version}")
    
    # 检查cgroup版本
    try:
        mount_info = subprocess.check_output(['mount']).decode()
        if 'cgroup2' in mount_info:
            print("✅ 使用cgroup v2")
        elif 'cgroup' in mount_info:
            print("⚠️  使用cgroup v1")
        else:
            print("❌ 未找到cgroup挂载")
    except:
        print("❌ 无法检查cgroup挂载")
    
    # 检查BPF JIT
    try:
        with open('/proc/sys/net/core/bpf_jit_enable', 'r') as f:
            jit_enabled = f.read().strip()
        print(f"BPF JIT: {'启用' if jit_enabled == '1' else '禁用'}")
    except:
        print("❌ 无法检查BPF JIT状态")
    
    # 检查权限
    if os.geteuid() == 0:
        print("✅ 运行在root权限下")
    else:
        print("❌ 需要root权限")
        return False
    
    return True

def test_simple_cgroup_bpf():
    """测试最简单的cgroup BPF程序"""
    print("\n=== 测试最简单的cgroup BPF程序 ===")
    
    # 最简单的cgroup BPF程序
    simple_bpf_code = """
int simple_cgroup_connect(struct bpf_sock_addr *ctx) {
    return 1;  // 总是允许
}
"""
    
    try:
        print("编译最简单的BPF程序...")
        bpf = BPF(text=simple_bpf_code)
        print("✅ 编译成功")
        
        print("尝试加载cgroup函数...")
        try:
            fn = bpf.load_func("simple_cgroup_connect", BPF.CGROUP_SOCK_ADDR)
            print("✅ 函数加载成功")
            return True, bpf, fn
        except Exception as e:
            print(f"❌ 函数加载失败: {e}")
            return False, None, None
            
    except Exception as e:
        print(f"❌ 编译失败: {e}")
        return False, None, None

def test_cgroup_attachment(bpf, fn):
    """测试cgroup附加"""
    print("\n=== 测试cgroup附加 ===")
    
    test_cgroup = "/sys/fs/cgroup/test_simple_cgroup"
    
    try:
        # 创建测试cgroup
        if not os.path.exists(test_cgroup):
            os.makedirs(test_cgroup)
        print(f"✅ 创建cgroup: {test_cgroup}")
        
        # 将当前进程加入cgroup
        with open(f"{test_cgroup}/cgroup.procs", "w") as f:
            f.write(str(os.getpid()))
        print(f"✅ 进程加入cgroup")
        
        # 尝试附加BPF程序
        try:
            bpf.attach_func(fn, test_cgroup, BPF.CGROUP_INET4_CONNECT)
            print("✅ BPF程序附加成功")
            
            # 分离程序
            bpf.detach_func(fn, test_cgroup, BPF.CGROUP_INET4_CONNECT)
            print("✅ BPF程序分离成功")
            
            return True
            
        except Exception as e:
            print(f"❌ BPF程序附加失败: {e}")
            return False
            
    except Exception as e:
        print(f"❌ cgroup操作失败: {e}")
        return False
    finally:
        # 清理
        try:
            if test_cgroup.startswith("/sys/fs/cgroup/test_"):
                os.rmdir(test_cgroup)
        except:
            pass

def analyze_bpf_path_only_issues():
    """分析bpf_path_only_control.c的具体问题"""
    print("\n=== 分析bpf_path_only_control.c问题 ===")
    
    bpf_file = "src/bpf_network_policy/bpf/bpf_path_only_control.c"
    
    if not os.path.exists(bpf_file):
        print(f"❌ 文件不存在: {bpf_file}")
        return False
    
    try:
        # 读取文件内容
        with open(bpf_file, 'r') as f:
            content = f.read()
        
        print("分析BPF程序内容...")
        
        # 检查函数签名
        import re
        functions = re.findall(r'int\s+(\w+)\s*\(([^)]*)\)', content)
        
        for func_name, params in functions:
            print(f"\n函数: {func_name}")
            print(f"  参数: {params}")
            
            # 分析参数类型
            if 'bpf_sock_addr' in params:
                print("  ✅ 使用正确的cgroup参数类型")
            elif 'pt_regs' in params:
                print("  ⚠️  使用kprobe参数类型")
            else:
                print("  ❌ 未知参数类型")
        
        # 尝试编译
        print("\n尝试编译原始BPF程序...")
        try:
            bpf = BPF(src_file=bpf_file)
            print("✅ 编译成功")
            
            # 尝试加载每个函数
            for func_name, params in functions:
                print(f"\n测试函数: {func_name}")
                
                if 'bpf_sock_addr' in params:
                    try:
                        fn = bpf.load_func(func_name, BPF.CGROUP_SOCK_ADDR)
                        print(f"  ✅ 加载成功 (CGROUP_SOCK_ADDR)")
                    except Exception as e:
                        print(f"  ❌ 加载失败: {str(e)[:100]}...")
                        
                        # 尝试分析错误原因
                        error_str = str(e).lower()
                        if 'invalid argument' in error_str:
                            print("    可能原因: 函数签名或返回值不正确")
                        elif 'permission denied' in error_str:
                            print("    可能原因: 权限不足或内核限制")
                        elif 'not supported' in error_str:
                            print("    可能原因: 内核不支持此BPF程序类型")
                
                elif 'pt_regs' in params:
                    try:
                        fn = bpf.load_func(func_name, BPF.KPROBE)
                        print(f"  ✅ 加载成功 (KPROBE)")
                    except Exception as e:
                        print(f"  ❌ 加载失败: {str(e)[:100]}...")
            
            return True
            
        except Exception as e:
            print(f"❌ 编译失败: {e}")
            return False
            
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return False

def provide_solutions():
    """提供解决方案"""
    print("\n=== 可能的解决方案 ===")
    
    print("1. 内核版本问题:")
    print("   - cgroup BPF需要内核4.10+")
    print("   - 某些功能需要更新的内核版本")
    print("   - 建议升级到5.4+内核")
    
    print("\n2. 内核配置问题:")
    print("   - 需要启用CONFIG_BPF=y")
    print("   - 需要启用CONFIG_BPF_SYSCALL=y") 
    print("   - 需要启用CONFIG_CGROUP_BPF=y")
    
    print("\n3. 函数签名问题:")
    print("   - cgroup函数必须使用struct bpf_sock_addr *参数")
    print("   - 返回值必须是int (0=拒绝, 1=允许)")
    print("   - 函数名不能包含static关键字")
    
    print("\n4. 权限问题:")
    print("   - 必须以root权限运行")
    print("   - 可能需要CAP_SYS_ADMIN权限")
    print("   - 检查SELinux/AppArmor限制")
    
    print("\n5. cgroup版本问题:")
    print("   - cgroup v1和v2的BPF支持不同")
    print("   - 建议使用cgroup v2")
    
    print("\n6. 替代方案:")
    print("   - 使用XDP进行网络拦截")
    print("   - 使用TC (Traffic Control) BPF")
    print("   - 使用LSM (Linux Security Module) BPF")
    print("   - 使用netfilter/iptables集成")

def main():
    """主函数"""
    print("cgroup BPF加载问题诊断工具")
    print("=" * 50)
    
    # 1. 检查系统支持
    if not check_system_support():
        return 1
    
    # 2. 测试简单的cgroup BPF
    success, bpf, fn = test_simple_cgroup_bpf()
    
    if success:
        # 3. 测试cgroup附加
        if test_cgroup_attachment(bpf, fn):
            print("\n✅ 基本cgroup BPF功能正常")
        else:
            print("\n❌ cgroup附加失败")
    else:
        print("\n❌ 基本cgroup BPF功能不可用")
    
    # 4. 分析具体的BPF程序问题
    analyze_bpf_path_only_issues()
    
    # 5. 提供解决方案
    provide_solutions()
    
    print("\n" + "=" * 50)
    print("诊断完成")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
