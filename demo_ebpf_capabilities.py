#!/usr/bin/env python3
"""
eBPF网络策略能力演示
展示eBPF程序的监控、策略检查和统计功能
"""

import os
import sys
import time
import socket
import threading
from bcc import BPF
import signal

class EBPFNetworkDemo:
    def __init__(self):
        self.bpf = None
        self.running = False
        self.events = []
        
    def load_ebpf_program(self):
        """加载eBPF程序"""
        print("=== 加载eBPF网络策略程序 ===")
        
        # 检查权限
        if os.geteuid() != 0:
            print("❌ 需要root权限运行此演示")
            return False
        
        # eBPF程序代码
        bpf_code = """
#include <uapi/linux/ptrace.h>
#include <linux/socket.h>
#include <linux/in.h>

// 连接事件结构
struct connect_event {
    u32 pid;
    u32 uid;
    u16 dport;
    u32 daddr;
    char comm[16];
    u8 policy_decision;  // 0=拒绝, 1=允许
    u32 policy_reason;   // 策略原因代码
};

// 事件输出
BPF_PERF_OUTPUT(connect_events);

// 端口策略映射
BPF_HASH(port_policy_map, u16, u32, 256);

// 进程白名单 - key: policy_id << 32 | process_hash, value: 1
BPF_HASH(process_allow_map, u64, u8, 1024);

// 进程黑名单 - key: policy_id << 32 | process_hash, value: 1
BPF_HASH(process_deny_map, u64, u8, 1024);

// IP白名单 - key: policy_id << 32 | ip_addr, value: 1
BPF_HASH(ip_allow_map, u64, u8, 1024);

// 统计信息
BPF_HASH(policy_stats, u32, u64, 10);

// 字符串哈希函数
static inline u32 hash_string(char *str) {
    u32 hash = 5381;
    for (int i = 0; i < 16 && str[i] != 0; i++) {
        hash = ((hash << 5) + hash) + str[i];
    }
    return hash;
}

// 网络连接策略检查
int trace_connect_policy(struct pt_regs *ctx) {
    struct connect_event event = {};
    
    // 获取进程信息
    event.pid = bpf_get_current_pid_tgid() >> 32;
    event.uid = bpf_get_current_uid_gid() & 0xFFFFFFFF;
    bpf_get_current_comm(&event.comm, sizeof(event.comm));
    
    // 获取连接信息
    struct sockaddr *addr = (struct sockaddr *)PT_REGS_PARM2(ctx);
    if (addr) {
        struct sockaddr_in *addr_in = (struct sockaddr_in *)addr;
        if (addr_in->sin_family == AF_INET) {
            event.dport = bpf_ntohs(addr_in->sin_port);
            event.daddr = addr_in->sin_addr.s_addr;
        }
    }
    
    // 更新总连接统计
    u32 total_key = 0;
    u64 *total_count = policy_stats.lookup(&total_key);
    if (total_count) {
        (*total_count)++;
    } else {
        u64 init_val = 1;
        policy_stats.update(&total_key, &init_val);
    }
    
    // 默认策略决策
    event.policy_decision = 1;  // 默认允许
    event.policy_reason = 0;    // 默认原因
    
    // 查找端口策略
    u32 *policy_id_ptr = port_policy_map.lookup(&event.dport);
    if (policy_id_ptr) {
        u32 policy_id = *policy_id_ptr;
        u32 comm_hash = hash_string(event.comm);
        
        // 检查进程黑名单
        u64 process_deny_key = ((u64)policy_id << 32) | comm_hash;
        u8 *process_denied = process_deny_map.lookup(&process_deny_key);
        if (process_denied && *process_denied == 1) {
            event.policy_decision = 0;  // 拒绝
            event.policy_reason = 1;    // 进程黑名单
            
            // 更新拒绝统计
            u32 denied_key = 2;
            u64 *denied_count = policy_stats.lookup(&denied_key);
            if (denied_count) {
                (*denied_count)++;
            } else {
                u64 init_val = 1;
                policy_stats.update(&denied_key, &init_val);
            }
        } else {
            // 检查IP白名单
            u64 ip_allow_key = ((u64)policy_id << 32) | event.daddr;
            u8 *ip_allowed = ip_allow_map.lookup(&ip_allow_key);
            
            // 检查进程白名单
            u64 process_allow_key = ((u64)policy_id << 32) | comm_hash;
            u8 *process_allowed = process_allow_map.lookup(&process_allow_key);
            
            if ((process_allowed && *process_allowed == 1) && 
                (ip_allowed && *ip_allowed == 1)) {
                event.policy_decision = 1;  // 允许
                event.policy_reason = 2;    // 白名单匹配
                
                // 更新允许统计
                u32 allowed_key = 1;
                u64 *allowed_count = policy_stats.lookup(&allowed_key);
                if (allowed_count) {
                    (*allowed_count)++;
                } else {
                    u64 init_val = 1;
                    policy_stats.update(&allowed_key, &init_val);
                }
            } else {
                event.policy_decision = 0;  // 拒绝
                event.policy_reason = 3;    // 不在白名单
                
                // 更新拒绝统计
                u32 denied_key = 2;
                u64 *denied_count = policy_stats.lookup(&denied_key);
                if (denied_count) {
                    (*denied_count)++;
                } else {
                    u64 init_val = 1;
                    policy_stats.update(&denied_key, &init_val);
                }
            }
        }
    }
    
    // 发送事件到用户空间
    connect_events.perf_submit(ctx, &event, sizeof(event));
    
    return 0;
}
"""
        
        try:
            print("编译eBPF程序...")
            self.bpf = BPF(text=bpf_code)
            print("✅ eBPF程序编译成功")
            
            # 附加到connect系统调用
            self.bpf.attach_kprobe(event="__sys_connect", fn_name="trace_connect_policy")
            print("✅ eBPF程序附加到网络系统调用")
            
            return True
            
        except Exception as e:
            print(f"❌ 加载eBPF程序失败: {e}")
            return False
    
    def configure_policies(self):
        """配置演示策略"""
        print("\n=== 配置网络策略 ===")
        
        if not self.bpf:
            return False
        
        try:
            # 获取BPF表
            port_policy_map = self.bpf.get_table("port_policy_map")
            process_allow_map = self.bpf.get_table("process_allow_map")
            process_deny_map = self.bpf.get_table("process_deny_map")
            ip_allow_map = self.bpf.get_table("ip_allow_map")
            
            # 配置端口策略 (端口 -> 策略ID)
            policies = {
                80: 1,    # HTTP
                443: 1,   # HTTPS
                53: 2,    # DNS
                8080: 3,  # 测试端口
            }
            
            for port, policy_id in policies.items():
                port_policy_map[port_policy_map.Key(port)] = port_policy_map.Leaf(policy_id)
            print(f"✅ 配置端口策略: {list(policies.keys())}")
            
            # 配置进程白名单
            allowed_processes = {
                1: ["python3", "curl", "wget"],  # 策略1允许的进程
                2: ["systemd-resolve", "dig"],   # 策略2允许的进程
            }
            
            for policy_id, processes in allowed_processes.items():
                for process in processes:
                    process_hash = self.hash_string_python(process)
                    key = (policy_id << 32) | process_hash
                    process_allow_map[process_allow_map.Key(key)] = process_allow_map.Leaf(1)
            print(f"✅ 配置进程白名单: {sum(len(p) for p in allowed_processes.values())} 个进程")
            
            # 配置进程黑名单
            denied_processes = {
                3: ["nc", "telnet"],  # 策略3拒绝的进程
            }
            
            for policy_id, processes in denied_processes.items():
                for process in processes:
                    process_hash = self.hash_string_python(process)
                    key = (policy_id << 32) | process_hash
                    process_deny_map[process_deny_map.Key(key)] = process_deny_map.Leaf(1)
            print(f"✅ 配置进程黑名单: {sum(len(p) for p in denied_processes.values())} 个进程")
            
            # 配置IP白名单
            allowed_ips = ["127.0.0.1", "*******", "*******"]
            for policy_id in [1, 2]:  # 策略1和2允许这些IP
                for ip_str in allowed_ips:
                    ip_int = self.ip_to_int(ip_str)
                    key = (policy_id << 32) | ip_int
                    ip_allow_map[ip_allow_map.Key(key)] = ip_allow_map.Leaf(1)
            print(f"✅ 配置IP白名单: {allowed_ips}")
            
            return True
            
        except Exception as e:
            print(f"❌ 配置策略失败: {e}")
            return False
    
    def hash_string_python(self, s):
        """Python版本的哈希函数，与BPF中的保持一致"""
        hash_val = 5381
        for i, c in enumerate(s):
            if i >= 16:
                break
            hash_val = ((hash_val << 5) + hash_val + ord(c)) & 0xFFFFFFFF
        return hash_val
    
    def ip_to_int(self, ip_str):
        """将IP字符串转换为整数"""
        parts = ip_str.split('.')
        return (int(parts[0]) << 24) | (int(parts[1]) << 16) | (int(parts[2]) << 8) | int(parts[3])
    
    def start_monitoring(self):
        """开始监控网络连接"""
        print("\n=== 开始网络监控 ===")
        
        if not self.bpf:
            return False
        
        # 设置事件处理器
        def handle_event(cpu, data, size):
            event = self.bpf["connect_events"].event(data)
            
            # 解析策略原因
            reason_map = {
                0: "默认策略",
                1: "进程黑名单",
                2: "白名单匹配", 
                3: "不在白名单"
            }
            
            event_info = {
                'timestamp': time.time(),
                'pid': event.pid,
                'comm': event.comm.decode('utf-8', 'replace'),
                'daddr': socket.inet_ntoa(event.daddr.to_bytes(4, 'little')),
                'dport': event.dport,
                'decision': "允许" if event.policy_decision else "拒绝",
                'reason': reason_map.get(event.policy_reason, "未知")
            }
            
            self.events.append(event_info)
            
            # 实时显示重要事件
            if event.policy_decision == 0:  # 拒绝的连接
                print(f"🚫 拒绝连接: {event_info['comm']}({event_info['pid']}) -> {event_info['daddr']}:{event_info['dport']} ({event_info['reason']})")
            elif event.policy_reason == 2:  # 白名单匹配
                print(f"✅ 允许连接: {event_info['comm']}({event_info['pid']}) -> {event_info['daddr']}:{event_info['dport']} ({event_info['reason']})")
        
        self.bpf["connect_events"].open_perf_buffer(handle_event)
        self.running = True
        
        print("✅ 网络监控已启动")
        print("监控所有网络连接并应用策略...")
        print("按 Ctrl+C 停止监控\n")
        
        try:
            while self.running:
                self.bpf.perf_buffer_poll(timeout=100)
        except KeyboardInterrupt:
            self.running = False
            print("\n监控已停止")
        
        return True
    
    def show_statistics(self):
        """显示统计信息"""
        print("\n=== 策略执行统计 ===")
        
        if not self.bpf:
            return
        
        try:
            policy_stats = self.bpf.get_table("policy_stats")
            
            total = 0
            allowed = 0
            denied = 0
            
            try:
                total = policy_stats[policy_stats.Key(0)].value
            except KeyError:
                pass
            
            try:
                allowed = policy_stats[policy_stats.Key(1)].value
            except KeyError:
                pass
            
            try:
                denied = policy_stats[policy_stats.Key(2)].value
            except KeyError:
                pass
            
            print(f"总连接数: {total}")
            print(f"允许连接: {allowed}")
            print(f"拒绝连接: {denied}")
            
            if total > 0:
                print(f"拒绝率: {denied/total*100:.1f}%")
            
            # 显示最近的事件
            if self.events:
                print(f"\n最近 {min(10, len(self.events))} 个连接事件:")
                for event in self.events[-10:]:
                    status = "🚫" if event['decision'] == "拒绝" else "✅"
                    print(f"  {status} {event['comm']}({event['pid']}) -> {event['daddr']}:{event['dport']} - {event['decision']} ({event['reason']})")
            
        except Exception as e:
            print(f"获取统计信息失败: {e}")
    
    def cleanup(self):
        """清理资源"""
        if self.bpf:
            try:
                self.bpf.detach_kprobe(event="__sys_connect")
                print("✅ eBPF程序已分离")
            except:
                pass

def main():
    """主函数"""
    print("eBPF网络策略能力演示")
    print("=" * 50)
    
    demo = EBPFNetworkDemo()
    
    try:
        # 加载eBPF程序
        if not demo.load_ebpf_program():
            return 1
        
        # 配置策略
        if not demo.configure_policies():
            return 1
        
        # 开始监控
        demo.start_monitoring()
        
        # 显示统计
        demo.show_statistics()
        
    except Exception as e:
        print(f"演示过程中出现错误: {e}")
        return 1
    finally:
        demo.cleanup()
    
    print("\n🎉 eBPF网络策略演示完成")
    return 0

if __name__ == "__main__":
    sys.exit(main())
