ports:
  - ports: ["80", "443"]
    process:
      process_allow_list:
        - "/usr/bin/obproxy"
        - "curl"
        - "wget"
      process_deny_list:
        - "/usr/bin/malicious"
        - "nc"
    cidr:
      cidr_allow_list:
        - "127.0.0.1/32"
        - "***********/24"
      cidr_deny_list:
        - "*******/32"
        - "*******/32"
  
  - ports: ["22"]
    process:
      process_allow_list:
        - "ssh"
        - "scp"
        - "sftp"
      process_deny_list: []
    cidr:
      cidr_allow_list:
        - "0.0.0.0/0"  # SSH允许所有IP
      cidr_deny_list: []
  
  - ports: ["53"]
    process:
      process_allow_list:
        - "systemd-resolve"
        - "dig"
        - "nslookup"
      process_deny_list: []
    cidr:
      cidr_allow_list:
        - "0.0.0.0/0"  # DNS允许所有IP
      cidr_deny_list: []
