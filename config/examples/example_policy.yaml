# Example BPF Network Policy Configuration
#
# This file demonstrates various policy configuration options
# for the BPF Network Policy Control System.

# Port-based access control policies
ports:
  # Web services (HTTP/HTTPS)
  - ports: ["80", "443"]
    process:
      process_allow_list:
        - "curl"
        - "wget"
        - "firefox"
        - "chrome"
        - "nginx"
        - "apache2"
      process_deny_list:
        - "nc"
        - "netcat"
        - "/usr/bin/malicious"
    cidr:
      cidr_allow_list:
        - "127.0.0.1/32"      # Localhost
        - "***********/24"    # Local network
        - "10.0.0.0/8"        # Private network
      cidr_deny_list:
        - "*******/32"        # Block specific DNS server
        - "*******/32"        # Block specific IP

  # SSH access
  - ports: ["22"]
    process:
      process_allow_list:
        - "ssh"
        - "scp"
        - "sftp"
        - "rsync"
      process_deny_list:
        - "telnet"
    cidr:
      cidr_allow_list:
        - "0.0.0.0/0"         # Allow from anywhere (be careful!)
      cidr_deny_list: []

  # DNS services
  - ports: ["53"]
    process:
      process_allow_list:
        - "systemd-resolve"
        - "dnsmasq"
        - "bind9"
        - "dig"
        - "nslookup"
      process_deny_list: []
    cidr:
      cidr_allow_list:
        - "0.0.0.0/0"         # Allow DNS to anywhere
      cidr_deny_list: []

  # Development/Testing ports
  - ports: ["8080", "8443", "3000", "5000"]
    process:
      process_allow_list:
        - "node"
        - "python3"
        - "java"
        - "nginx"
        - "apache2"
        - "docker"
      process_deny_list:
        - "nc"
        - "netcat"
    cidr:
      cidr_allow_list:
        - "127.0.0.1/32"      # Localhost only
        - "***********/16"    # Local networks
        - "10.0.0.0/8"        # Private networks
      cidr_deny_list:
        - "0.0.0.0/1"         # Block most external access
        - "*********/1"

  # Database ports (restrictive)
  - ports: ["3306", "5432", "27017", "6379"]
    process:
      process_allow_list:
        - "mysql"
        - "mysqld"
        - "postgres"
        - "mongod"
        - "redis-server"
        - "python3"           # For database clients
        - "java"              # For database clients
      process_deny_list:
        - "nc"
        - "netcat"
        - "telnet"
    cidr:
      cidr_allow_list:
        - "127.0.0.1/32"      # Localhost only
        - "***********/24"    # Specific local subnet
      cidr_deny_list:
        - "0.0.0.0/0"         # Block all external access

# Configuration notes:
# 1. Process names should match the actual process command name
# 2. CIDR blocks support both individual IPs (/32) and network ranges
# 3. Empty lists mean no restrictions for that category
# 4. Deny lists take precedence over allow lists
# 5. Both process and CIDR rules must pass for access to be granted
