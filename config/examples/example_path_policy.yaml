# Example BPF Network Policy Configuration with Process Path Support
#
# This file demonstrates how to use process absolute paths for more precise
# process control in the BPF Network Policy Control System.

# Port-based access control policies with process path support
ports:
  # Web services (HTTP/HTTPS) - Using both process names and paths
  - ports: ["80", "443"]
    process:
      # Traditional process name whitelist
      process_allow_list:
        - "curl"
        - "wget"
        - "firefox"
        - "chrome"
        - "nginx"
        - "apache2"
      
      # Process path whitelist - more precise control
      process_path_allow_list:
        - "/usr/bin/curl"
        - "/usr/bin/wget"
        - "/usr/bin/firefox"
        - "/usr/bin/google-chrome"
        - "/usr/sbin/nginx"
        - "/usr/sbin/apache2"
        - "/opt/google/chrome/chrome"
        - "/snap/firefox/current/usr/lib/firefox/firefox"
      
      # Process name blacklist
      process_deny_list:
        - "nc"
        - "netcat"
      
      # Process path blacklist - block specific malicious paths
      process_path_deny_list:
        - "/tmp/malicious_tool"
        - "/var/tmp/suspicious_binary"
        - "/usr/bin/malicious"
        - "/home/<USER>/Downloads/untrusted_app"
    
    cidr:
      cidr_allow_list:
        - "127.0.0.1/32"      # Localhost
        - "***********/24"    # Local network
        - "10.0.0.0/8"        # Private network
      cidr_deny_list:
        - "*******/32"        # Block specific DNS server
        - "*******/32"        # Block specific IP

  # SSH access - Strict path-based control
  - ports: ["22"]
    process:
      # Allow only specific SSH client paths
      process_path_allow_list:
        - "/usr/bin/ssh"
        - "/usr/bin/scp"
        - "/usr/bin/sftp"
        - "/usr/bin/rsync"
        - "/opt/openssh/bin/ssh"
      
      # Block any SSH tools from suspicious locations
      process_path_deny_list:
        - "/tmp/ssh"
        - "/var/tmp/ssh"
        - "/home/<USER>/ssh"
        - "/usr/local/bin/backdoor_ssh"
    
    cidr:
      cidr_allow_list:
        - "0.0.0.0/0"         # Allow from anywhere (be careful!)
      cidr_deny_list: []

  # Database ports - Very restrictive path-based control
  - ports: ["3306", "5432", "27017", "6379"]
    process:
      # Only allow database servers and specific client tools
      process_path_allow_list:
        - "/usr/sbin/mysqld"
        - "/usr/bin/mysql"
        - "/usr/lib/postgresql/*/bin/postgres"
        - "/usr/bin/psql"
        - "/usr/bin/mongod"
        - "/usr/bin/mongo"
        - "/usr/bin/redis-server"
        - "/usr/bin/redis-cli"
        - "/opt/mysql/bin/mysqld"
        - "/opt/postgresql/bin/postgres"
        - "/usr/bin/python3"      # For database clients
        - "/usr/bin/java"         # For database clients
      
      # Block any database tools from untrusted locations
      process_path_deny_list:
        - "/tmp/mysql"
        - "/tmp/postgres"
        - "/tmp/mongo"
        - "/tmp/redis"
        - "/home/<USER>/mysql"
        - "/var/tmp/db_tool"
    
    cidr:
      cidr_allow_list:
        - "127.0.0.1/32"      # Localhost only
        - "***********/24"    # Specific local subnet
      cidr_deny_list:
        - "0.0.0.0/0"         # Block all external access

  # Development ports - Mixed name and path control
  - ports: ["8080", "8443", "3000", "5000"]
    process:
      # Allow common development tools by name
      process_allow_list:
        - "node"
        - "python3"
        - "java"
        - "nginx"
        - "docker"
      
      # Allow specific development tools by path
      process_path_allow_list:
        - "/usr/bin/node"
        - "/usr/bin/nodejs"
        - "/usr/bin/python3"
        - "/usr/bin/java"
        - "/usr/bin/docker"
        - "/snap/node/current/bin/node"
        - "/opt/node/bin/node"
        - "/usr/local/bin/node"
      
      # Block development tools from suspicious locations
      process_path_deny_list:
        - "/tmp/node"
        - "/tmp/python"
        - "/tmp/java"
        - "/var/tmp/dev_tool"
        - "/home/<USER>/Downloads/node"
    
    cidr:
      cidr_allow_list:
        - "127.0.0.1/32"      # Localhost only
        - "***********/16"    # Local networks
        - "10.0.0.0/8"        # Private networks
      cidr_deny_list:
        - "0.0.0.0/1"         # Block most external access
        - "*********/1"

# Configuration notes for process path support:
# 1. Process paths must be absolute paths starting with '/'
# 2. Process path matching is exact - no wildcards or regex support yet
# 3. Both process_allow_list and process_path_allow_list are checked (OR logic)
# 4. Both process_deny_list and process_path_deny_list are checked (OR logic)
# 5. Deny lists take precedence over allow lists
# 6. Process path checking provides more security than process name checking
# 7. Use process paths to prevent process name spoofing attacks
# 8. Consider using both name and path lists for compatibility
# 9. Process paths are resolved at runtime, so symlinks may affect matching
# 10. Long paths are supported but may impact performance due to hashing
