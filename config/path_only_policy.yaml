# 基于进程路径的网络策略配置
# 只包含进程路径管控，不包含进程名管控

ports:
  # HTTP/HTTPS 端口策略
  - ports: ["80", "443"]
    process_path:
      process_path_allow_list:
        - "/usr/bin/curl"
        - "/usr/bin/wget"
        - "/usr/bin/firefox"
        - "/usr/bin/chromium-browser"
        - "/usr/bin/google-chrome"
        - "/opt/google/chrome/chrome"
        - "/usr/bin/python3"
        - "/usr/bin/python"
      process_path_deny_list:
        - "/usr/bin/nc"
        - "/usr/bin/netcat"
        - "/usr/bin/telnet"
        - "/tmp/malicious"
    cidr:
      cidr_allow_list:
        - "0.0.0.0/0"  # 允许所有IP（可根据需要限制）
      cidr_deny_list:
        - "*************/24"  # 示例：拒绝特定网段
  
  # SSH 端口策略
  - ports: ["22"]
    process_path:
      process_path_allow_list:
        - "/usr/bin/ssh"
        - "/usr/bin/scp"
        - "/usr/bin/sftp"
        - "/usr/bin/rsync"
      process_path_deny_list:
        - "/usr/bin/nc"
        - "/tmp/backdoor"
    cidr:
      cidr_allow_list:
        - "0.0.0.0/0"  # SSH允许所有IP
      cidr_deny_list: []
  
  # DNS 端口策略
  - ports: ["53"]
    process_path:
      process_path_allow_list:
        - "/usr/bin/dig"
        - "/usr/bin/nslookup"
        - "/usr/bin/host"
        - "/usr/sbin/systemd-resolved"
        - "/usr/bin/systemd-resolve"
        - "/usr/bin/python3"
        - "/usr/bin/curl"
        - "/usr/bin/wget"
      process_path_deny_list: []
    cidr:
      cidr_allow_list:
        - "0.0.0.0/0"  # DNS允许所有IP
      cidr_deny_list: []

  # 数据库端口策略 (MySQL)
  - ports: ["3306"]
    process_path:
      process_path_allow_list:
        - "/usr/bin/mysql"
        - "/usr/bin/mysqldump"
        - "/usr/bin/python3"  # 允许Python应用连接数据库
        - "/usr/bin/java"     # 允许Java应用连接数据库
        - "/opt/myapp/bin/myapp"  # 允许特定应用
      process_path_deny_list:
        - "/usr/bin/nc"
        - "/usr/bin/telnet"
        - "/tmp/exploit"
    cidr:
      cidr_allow_list:
        - "127.0.0.1/32"      # 只允许本地连接
        - "***********/24"    # 允许内网连接
      cidr_deny_list:
        - "0.0.0.0/0"         # 默认拒绝所有外网

  # Redis 端口策略
  - ports: ["6379"]
    process_path:
      process_path_allow_list:
        - "/usr/bin/redis-cli"
        - "/usr/bin/python3"
        - "/usr/bin/node"
        - "/opt/myapp/bin/myapp"
      process_path_deny_list:
        - "/usr/bin/nc"
        - "/usr/bin/telnet"
    cidr:
      cidr_allow_list:
        - "127.0.0.1/32"      # 只允许本地连接
        - "***********/24"    # 允许内网连接
      cidr_deny_list: []

  # 自定义应用端口策略
  - ports: ["8080", "8443"]
    process_path:
      process_path_allow_list:
        - "/usr/bin/curl"
        - "/usr/bin/wget"
        - "/usr/bin/python3"
        - "/opt/myapp/bin/client"
      process_path_deny_list:
        - "/usr/bin/nc"
        - "/usr/bin/telnet"
        - "/tmp/scanner"
    cidr:
      cidr_allow_list:
        - "127.0.0.1/32"
        - "***********/24"
      cidr_deny_list:
        - "10.0.0.0/8"        # 拒绝特定网段
