# MANIFEST.in - Package Manifest for BPF Network Policy Control System

# Include documentation files
include docs/README.md
include docs/DEPLOYMENT_GUIDE.md
include docs/OPTIMIZATION_REPORT.md
recursive-include docs *.md *.rst *.txt

# Include configuration files
include config/*.yaml
include config/*.yml
recursive-include config/examples *.yaml *.yml

# Include BPF programs
recursive-include src/bpf_network_policy/bpf *.c *.h

# Include project configuration files
include requirements.txt
include setup.py
include setup.cfg
include pyproject.toml
include MANIFEST.in

# Include license and other legal files
include LICENSE
include NOTICE
include AUTHORS

# Include version control files
include .gitignore

# Include test configuration
include tests/conftest.py
recursive-include tests *.py

# Include scripts
recursive-include scripts *.sh *.py

# Exclude compiled Python files
global-exclude *.pyc
global-exclude *.pyo
global-exclude *.pyd
global-exclude __pycache__
global-exclude *.so
global-exclude .DS_Store

# Exclude development and build artifacts
global-exclude .git*
global-exclude .tox
global-exclude .coverage
global-exclude .pytest_cache
global-exclude build
global-exclude dist
global-exclude *.egg-info
