#!/usr/bin/env python3
"""
测试项目中所有BPF代码的可用性
验证每个BPF C文件是否能够正常编译和工作
"""

import os
import sys
import time
from bcc import BPF
import glob

class BPFCodeTester:
    def __init__(self):
        self.test_results = {}
        self.bpf_files = []
        
    def find_bpf_files(self):
        """查找所有BPF C文件"""
        print("=== 查找BPF代码文件 ===")
        
        bpf_dir = "src/bpf_network_policy/bpf"
        if not os.path.exists(bpf_dir):
            print(f"❌ BPF目录不存在: {bpf_dir}")
            return False
        
        # 查找所有.c文件
        pattern = os.path.join(bpf_dir, "*.c")
        self.bpf_files = glob.glob(pattern)
        
        if not self.bpf_files:
            print("❌ 未找到BPF C文件")
            return False
        
        print(f"✅ 找到 {len(self.bpf_files)} 个BPF文件:")
        for i, file_path in enumerate(self.bpf_files, 1):
            filename = os.path.basename(file_path)
            print(f"  {i}. {filename}")
        
        return True
    
    def test_bpf_compilation(self, bpf_file):
        """测试单个BPF文件的编译"""
        filename = os.path.basename(bpf_file)
        print(f"\n--- 测试 {filename} ---")
        
        result = {
            'file': filename,
            'path': bpf_file,
            'compilation': False,
            'tables': [],
            'functions': [],
            'error': None,
            'file_size': 0,
            'line_count': 0
        }
        
        try:
            # 获取文件信息
            if os.path.exists(bpf_file):
                result['file_size'] = os.path.getsize(bpf_file)
                with open(bpf_file, 'r') as f:
                    result['line_count'] = len(f.readlines())
                print(f"  文件大小: {result['file_size']} 字节")
                print(f"  代码行数: {result['line_count']} 行")
            else:
                result['error'] = "文件不存在"
                return result
            
            # 尝试编译BPF程序
            print("  编译BPF程序...")
            bpf = BPF(src_file=bpf_file)
            result['compilation'] = True
            print("  ✅ 编译成功")
            
            # 获取BPF表信息
            print("  检查BPF表...")
            try:
                # 尝试获取常见的表名
                common_tables = [
                    'port_policy_map', 'process_allow_map', 'process_deny_map',
                    'process_path_allow_map', 'process_path_deny_map',
                    'cidr_allow_map', 'cidr_deny_map', 'policy_stats'
                ]
                
                for table_name in common_tables:
                    try:
                        table = bpf.get_table(table_name)
                        result['tables'].append(table_name)
                        print(f"    ✅ 表 {table_name}")
                    except KeyError:
                        pass  # 表不存在，跳过
                
                if result['tables']:
                    print(f"  ✅ 找到 {len(result['tables'])} 个BPF表")
                else:
                    print("  ⚠️  未找到预期的BPF表")
                
            except Exception as e:
                result['error'] = f"获取BPF表失败: {e}"
                print(f"  ❌ 获取BPF表失败: {e}")
            
            # 测试表操作（如果有表的话）
            if result['tables']:
                print("  测试表操作...")
                try:
                    self.test_table_operations(bpf, result['tables'])
                    print("  ✅ 表操作测试通过")
                except Exception as e:
                    print(f"  ⚠️  表操作测试失败: {e}")
            
        except Exception as e:
            result['compilation'] = False
            result['error'] = str(e)
            print(f"  ❌ 编译失败: {e}")
        
        return result
    
    def test_table_operations(self, bpf, table_names):
        """测试BPF表的基本操作"""
        for table_name in table_names[:3]:  # 只测试前3个表，避免过多输出
            try:
                table = bpf.get_table(table_name)
                
                # 测试写入操作
                if 'port_policy_map' in table_name:
                    # 端口映射表测试
                    table[table.Key(80)] = table.Leaf(1)
                    table[table.Key(443)] = table.Leaf(1)
                elif 'process_allow_map' in table_name or 'process_deny_map' in table_name:
                    # 进程映射表测试
                    test_key = (1 << 32) | 0x12345678
                    table[table.Key(test_key)] = table.Leaf(1)
                elif 'policy_stats' in table_name:
                    # 统计表测试
                    table[table.Key(0)] = table.Leaf(100)
                
                # 测试读取操作
                count = 0
                for key, value in table.items():
                    count += 1
                    if count >= 5:  # 限制输出数量
                        break
                
            except Exception as e:
                print(f"    表 {table_name} 操作失败: {e}")
    
    def test_hash_functions(self):
        """测试哈希函数的一致性"""
        print("\n=== 测试哈希函数一致性 ===")
        
        def hash_string_python(s):
            """Python版本的哈希函数"""
            hash_val = 5381
            for i, c in enumerate(s):
                if i >= 16:
                    break
                hash_val = ((hash_val << 5) + hash_val + ord(c)) & 0xFFFFFFFF
            return hash_val
        
        # 测试字符串
        test_strings = [
            "python3", "curl", "wget", "ssh", "nc", "telnet",
            "/usr/bin/python3", "/usr/bin/curl", "/bin/bash"
        ]
        
        print("哈希函数测试结果:")
        print("字符串                  哈希值")
        print("-" * 40)
        
        for s in test_strings:
            hash_val = hash_string_python(s)
            print(f"{s:<20} 0x{hash_val:08x} ({hash_val})")
        
        return True
    
    def analyze_bpf_code(self, bpf_file):
        """分析BPF代码内容"""
        filename = os.path.basename(bpf_file)
        
        try:
            with open(bpf_file, 'r') as f:
                content = f.read()
            
            # 统计关键元素
            hash_maps = content.count('BPF_HASH')
            functions = content.count('int ')
            includes = content.count('#include')
            
            print(f"  代码分析:")
            print(f"    包含文件: {includes}")
            print(f"    BPF哈希表: {hash_maps}")
            print(f"    函数定义: {functions}")
            
            # 查找特定功能
            features = []
            if 'port_policy_map' in content:
                features.append('端口策略')
            if 'process_allow_map' in content:
                features.append('进程白名单')
            if 'process_deny_map' in content:
                features.append('进程黑名单')
            if 'cidr_allow_map' in content:
                features.append('IP白名单')
            if 'policy_stats' in content:
                features.append('统计功能')
            if 'hash_string' in content:
                features.append('哈希计算')
            if 'get_process_path' in content:
                features.append('路径获取')
            
            if features:
                print(f"    功能特性: {', '.join(features)}")
            
        except Exception as e:
            print(f"  代码分析失败: {e}")
    
    def run_all_tests(self):
        """运行所有测试"""
        print("BPF代码可用性测试")
        print("=" * 50)
        
        # 检查权限
        if os.geteuid() != 0:
            print("❌ 需要root权限来编译和测试BPF程序")
            print("请使用: sudo python3 test_all_bpf_codes.py")
            return False
        
        # 查找BPF文件
        if not self.find_bpf_files():
            return False
        
        # 测试每个BPF文件
        print(f"\n=== 测试 {len(self.bpf_files)} 个BPF文件 ===")
        
        for bpf_file in self.bpf_files:
            # 分析代码
            self.analyze_bpf_code(bpf_file)
            
            # 测试编译
            result = self.test_bpf_compilation(bpf_file)
            self.test_results[result['file']] = result
        
        # 测试哈希函数
        self.test_hash_functions()
        
        # 显示总结
        self.show_summary()
        
        return True
    
    def show_summary(self):
        """显示测试总结"""
        print("\n" + "=" * 50)
        print("测试结果总结")
        print("=" * 50)
        
        total_files = len(self.test_results)
        successful_compilations = sum(1 for r in self.test_results.values() if r['compilation'])
        
        print(f"总文件数: {total_files}")
        print(f"编译成功: {successful_compilations}")
        print(f"编译失败: {total_files - successful_compilations}")
        print(f"成功率: {successful_compilations/total_files*100:.1f}%")
        
        print(f"\n详细结果:")
        for filename, result in self.test_results.items():
            status = "✅" if result['compilation'] else "❌"
            tables_info = f"({len(result['tables'])} 表)" if result['tables'] else ""
            error_info = f" - {result['error']}" if result['error'] and not result['compilation'] else ""
            
            print(f"  {status} {filename:<25} {tables_info:<10} {error_info}")
        
        # 显示功能统计
        all_tables = set()
        for result in self.test_results.values():
            all_tables.update(result['tables'])
        
        if all_tables:
            print(f"\n发现的BPF表类型:")
            for table in sorted(all_tables):
                print(f"  - {table}")
        
        # 总体评价
        if successful_compilations == total_files:
            print(f"\n🎉 所有BPF代码都可以正常使用！")
        elif successful_compilations > 0:
            print(f"\n⚠️  部分BPF代码可用，需要修复编译失败的文件")
        else:
            print(f"\n❌ 所有BPF代码都无法编译，需要检查环境配置")

def main():
    """主函数"""
    tester = BPFCodeTester()
    success = tester.run_all_tests()
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
