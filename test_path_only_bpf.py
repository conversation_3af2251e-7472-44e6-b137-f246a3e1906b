#!/usr/bin/env python3
"""
测试专门的进程路径管控BPF代码
"""

import os
import sys
import time
from bcc import BPF

def test_path_only_bpf():
    """测试仅基于路径的BPF代码"""
    print("=== 测试进程路径专用BPF代码 ===")
    
    # 检查权限
    if os.geteuid() != 0:
        print("❌ 需要root权限")
        return False
    
    try:
        # 使用新创建的路径专用BPF程序
        bpf_file = "src/bpf_network_policy/bpf/bpf_path_only_control.c"
        if not os.path.exists(bpf_file):
            print(f"❌ BPF文件不存在: {bpf_file}")
            return False
        
        print(f"编译BPF程序: {bpf_file}")
        bpf = BPF(src_file=bpf_file)
        print("✅ BPF程序编译成功")
        
        # 测试BPF表
        print("\n测试BPF表...")
        
        # 获取表
        port_policy_map = bpf.get_table("port_policy_map")
        process_path_allow_map = bpf.get_table("process_path_allow_map")
        process_path_deny_map = bpf.get_table("process_path_deny_map")
        cidr_allow_map = bpf.get_table("cidr_allow_map")
        cidr_deny_map = bpf.get_table("cidr_deny_map")
        policy_stats = bpf.get_table("policy_stats")
        
        print("✅ 成功获取所有BPF表")
        
        # 测试表操作
        print("\n测试表操作...")
        
        # 添加端口策略
        port_policy_map[port_policy_map.Key(80)] = port_policy_map.Leaf(1)
        port_policy_map[port_policy_map.Key(443)] = port_policy_map.Leaf(1)
        port_policy_map[port_policy_map.Key(22)] = port_policy_map.Leaf(2)
        print("✅ 添加端口策略")
        
        # 添加进程路径白名单
        def hash_long_string_python(s):
            """Python版本的长字符串哈希函数"""
            hash_val = 5381
            for i, c in enumerate(s):
                if i >= 256:  # 限制长度
                    break
                hash_val = ((hash_val << 5) + hash_val + ord(c)) & 0xFFFFFFFF
            return hash_val
        
        # 测试路径
        test_paths = [
            "/usr/bin/curl",
            "/usr/bin/wget", 
            "/usr/bin/python3",
            "/usr/bin/ssh",
            "/usr/bin/nc"  # 这个会加到黑名单
        ]
        
        policy_id = 1
        for path in test_paths[:4]:  # 前4个加到白名单
            path_hash = hash_long_string_python(path)
            key = (policy_id << 32) | path_hash
            process_path_allow_map[process_path_allow_map.Key(key)] = process_path_allow_map.Leaf(1)
            print(f"  ✅ 添加路径白名单: {path}")
        
        # 添加进程路径黑名单
        nc_path = "/usr/bin/nc"
        nc_hash = hash_long_string_python(nc_path)
        nc_key = (policy_id << 32) | nc_hash
        process_path_deny_map[process_path_deny_map.Key(nc_key)] = process_path_deny_map.Leaf(1)
        print(f"  ✅ 添加路径黑名单: {nc_path}")
        
        # 添加IP白名单
        def ip_to_int(ip_str):
            parts = ip_str.split('.')
            return (int(parts[0]) << 24) | (int(parts[1]) << 16) | (int(parts[2]) << 8) | int(parts[3])
        
        allowed_ips = ["127.0.0.1", "***********", "*******"]
        for ip_str in allowed_ips:
            ip_int = ip_to_int(ip_str)
            key = (policy_id << 32) | ip_int
            cidr_allow_map[cidr_allow_map.Key(key)] = cidr_allow_map.Leaf(1)
            print(f"  ✅ 添加IP白名单: {ip_str}")
        
        # 验证表内容
        print("\n验证表内容...")
        
        # 检查端口策略
        port_count = 0
        for key, value in port_policy_map.items():
            port_count += 1
            print(f"  端口 {key.value} -> 策略ID {value.value}")
        print(f"✅ 端口策略数量: {port_count}")
        
        # 检查进程路径白名单
        path_allow_count = 0
        for key, value in process_path_allow_map.items():
            path_allow_count += 1
            policy_id = key.value >> 32
            path_hash = key.value & 0xFFFFFFFF
            print(f"  路径白名单: 策略ID {policy_id}, 路径哈希 0x{path_hash:x}")
        print(f"✅ 进程路径白名单数量: {path_allow_count}")
        
        # 检查进程路径黑名单
        path_deny_count = 0
        for key, value in process_path_deny_map.items():
            path_deny_count += 1
            policy_id = key.value >> 32
            path_hash = key.value & 0xFFFFFFFF
            print(f"  路径黑名单: 策略ID {policy_id}, 路径哈希 0x{path_hash:x}")
        print(f"✅ 进程路径黑名单数量: {path_deny_count}")
        
        # 检查IP白名单
        ip_allow_count = 0
        for key, value in cidr_allow_map.items():
            ip_allow_count += 1
        print(f"✅ IP白名单数量: {ip_allow_count}")
        
        return True
        
    except Exception as e:
        print(f"❌ 路径专用BPF测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_hash_consistency():
    """测试哈希函数一致性"""
    print("\n=== 测试路径哈希函数一致性 ===")
    
    def hash_long_string_python(s):
        """Python版本的长字符串哈希函数"""
        hash_val = 5381
        for i, c in enumerate(s):
            if i >= 256:
                break
            hash_val = ((hash_val << 5) + hash_val + ord(c)) & 0xFFFFFFFF
        return hash_val
    
    # 测试路径
    test_paths = [
        "/usr/bin/curl",
        "/usr/bin/wget",
        "/usr/bin/python3",
        "/usr/bin/ssh",
        "/usr/bin/nc",
        "/opt/google/chrome/chrome",
        "/usr/bin/firefox",
        "/tmp/malicious_program",
        "/home/<USER>/myapp/bin/myapp"
    ]
    
    print("进程路径哈希值:")
    print("路径                                    哈希值")
    print("-" * 70)
    
    for path in test_paths:
        hash_val = hash_long_string_python(path)
        print(f"{path:<35} 0x{hash_val:08x} ({hash_val})")
    
    return True

def test_policy_logic():
    """测试策略逻辑"""
    print("\n=== 测试路径策略逻辑 ===")
    
    def hash_long_string_python(s):
        hash_val = 5381
        for i, c in enumerate(s):
            if i >= 256:
                break
            hash_val = ((hash_val << 5) + hash_val + ord(c)) & 0xFFFFFFFF
        return hash_val
    
    def check_path_policy(process_path, port, policy_id=1):
        """模拟BPF程序中的路径策略检查逻辑"""
        
        # 计算路径哈希
        path_hash = hash_long_string_python(process_path)
        process_key = (policy_id << 32) | path_hash
        
        # 模拟的黑名单
        deny_list = {
            (1 << 32) | hash_long_string_python("/usr/bin/nc"): True,
            (1 << 32) | hash_long_string_python("/tmp/malicious"): True,
        }
        
        # 模拟的白名单
        allow_list = {
            (1 << 32) | hash_long_string_python("/usr/bin/curl"): True,
            (1 << 32) | hash_long_string_python("/usr/bin/wget"): True,
            (1 << 32) | hash_long_string_python("/usr/bin/python3"): True,
            (1 << 32) | hash_long_string_python("/usr/bin/ssh"): True,
        }
        
        # 检查黑名单
        if process_key in deny_list:
            return False, "在路径黑名单中"
        
        # 检查白名单
        if process_key in allow_list:
            return True, "在路径白名单中"
        
        return False, "不在路径白名单中"
    
    # 测试不同的路径和端口组合
    test_cases = [
        ("/usr/bin/curl", 80),
        ("/usr/bin/wget", 443),
        ("/usr/bin/python3", 80),
        ("/usr/bin/ssh", 22),
        ("/usr/bin/nc", 80),
        ("/tmp/malicious", 443),
        ("/usr/bin/unknown", 80),
    ]
    
    print("路径策略检查结果:")
    print("进程路径                        端口    结果    原因")
    print("-" * 65)
    
    for process_path, port in test_cases:
        allowed, reason = check_path_policy(process_path, port)
        status = "允许" if allowed else "拒绝"
        print(f"{process_path:<30} {port:>4}    {status:<4}  {reason}")
    
    return True

def main():
    """主函数"""
    print("进程路径专用BPF代码测试")
    print("=" * 50)
    
    success = True
    
    # 测试BPF编译和表操作
    if not test_path_only_bpf():
        success = False
    
    # 测试哈希函数
    if not test_hash_consistency():
        success = False
    
    # 测试策略逻辑
    if not test_policy_logic():
        success = False
    
    if success:
        print("\n🎉 进程路径专用BPF代码测试成功！")
        print("✅ BPF程序编译正常")
        print("✅ 路径哈希计算正确")
        print("✅ 策略逻辑正确")
        print("✅ 表操作正常")
        print("\n这个版本专门针对进程路径管控，不包含进程名管控")
    else:
        print("\n❌ 进程路径专用BPF代码测试失败")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
