#!/bin/bash
# BPF Network Policy Control System - Installation Script
#
# This script automates the installation of the BPF Network Policy Control System
# including all system dependencies and Python packages.

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
check_root() {
    if [[ $EUID -eq 0 ]]; then
        log_error "This script should not be run as root"
        log_info "Please run as a regular user with sudo privileges"
        exit 1
    fi
}

# Detect OS
detect_os() {
    if [[ -f /etc/os-release ]]; then
        . /etc/os-release
        OS=$ID
        VERSION=$VERSION_ID
    else
        log_error "Cannot detect operating system"
        exit 1
    fi
    
    log_info "Detected OS: $OS $VERSION"
}

# Install system dependencies
install_system_deps() {
    log_info "Installing system dependencies..."
    
    case $OS in
        ubuntu|debian)
            sudo apt-get update
            sudo apt-get install -y \
                bpfcc-tools \
                python3-bpfcc \
                linux-headers-$(uname -r) \
                build-essential \
                clang \
                llvm \
                python3-pip \
                python3-venv \
                git
            ;;
        centos|rhel|fedora)
            if command -v dnf &> /dev/null; then
                PKG_MGR="dnf"
            else
                PKG_MGR="yum"
                sudo yum install -y epel-release
            fi
            
            sudo $PKG_MGR install -y \
                bcc-tools \
                python3-bcc \
                kernel-devel \
                gcc \
                clang \
                llvm \
                python3-pip \
                git
            ;;
        *)
            log_error "Unsupported operating system: $OS"
            exit 1
            ;;
    esac
    
    log_success "System dependencies installed"
}

# Verify BCC installation
verify_bcc() {
    log_info "Verifying BCC installation..."
    
    if python3 -c "from bcc import BPF; print('BCC installed successfully')" 2>/dev/null; then
        log_success "BCC verification passed"
    else
        log_error "BCC verification failed"
        log_info "Please check BCC installation manually"
        exit 1
    fi
}

# Install Python dependencies
install_python_deps() {
    log_info "Installing Python dependencies..."
    
    # Create virtual environment if it doesn't exist
    if [[ ! -d "venv" ]]; then
        python3 -m venv venv
        log_info "Created virtual environment"
    fi
    
    # Activate virtual environment
    source venv/bin/activate
    
    # Upgrade pip
    pip install --upgrade pip
    
    # Install dependencies
    pip install -r requirements.txt
    
    # Install package in development mode
    pip install -e .
    
    log_success "Python dependencies installed"
}

# Create directories
create_directories() {
    log_info "Creating system directories..."
    
    sudo mkdir -p /etc/bpf-network-policy
    sudo mkdir -p /var/log/bpf-network-policy
    sudo mkdir -p /var/lib/bpf-network-policy
    
    # Copy configuration files
    sudo cp config/policy_config.yaml /etc/bpf-network-policy/
    sudo cp -r config/examples /etc/bpf-network-policy/
    
    # Set permissions
    sudo chown -R root:root /etc/bpf-network-policy
    sudo chmod 644 /etc/bpf-network-policy/*.yaml
    sudo chmod -R 644 /etc/bpf-network-policy/examples/
    
    log_success "System directories created"
}

# Run tests
run_tests() {
    log_info "Running tests..."
    
    source venv/bin/activate
    
    # Run unit tests
    python -m pytest tests/unit/ -v
    
    # Run integration tests (non-root parts)
    python -m pytest tests/integration/ -v -m "not requires_root"
    
    log_success "Tests completed"
}

# Main installation function
main() {
    log_info "Starting BPF Network Policy Control System installation..."
    
    check_root
    detect_os
    install_system_deps
    verify_bcc
    install_python_deps
    create_directories
    run_tests
    
    log_success "Installation completed successfully!"
    log_info ""
    log_info "Next steps:"
    log_info "1. Activate the virtual environment: source venv/bin/activate"
    log_info "2. Test the installation: bpf-policy-test"
    log_info "3. Run with root privileges: sudo bpf-policy-control"
    log_info ""
    log_info "For more information, see docs/DEPLOYMENT_GUIDE.md"
}

# Run main function
main "$@"
