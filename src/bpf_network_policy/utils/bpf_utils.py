#!/usr/bin/env python3
"""
BPF Network Policy Utilities

This module provides shared utilities for BPF network policy management,
including IP address handling, CIDR parsing, string hashing, and common
validation functions used across the project.
"""

import socket
import struct
import ipaddress
import logging
import os
import sys
from typing import Dict, List, Tuple, Optional, Union, Any
import yaml

# Configure module logger
logger = logging.getLogger(__name__)


class BPFUtilsError(Exception):
    """Base exception for BPF utilities errors."""
    pass


class IPAddressError(BPFUtilsError):
    """Exception for IP address related errors."""
    pass


class CIDRError(BPFUtilsError):
    """Exception for CIDR parsing errors."""
    pass


class PolicyError(BPFUtilsError):
    """Exception for policy validation errors."""
    pass


class NetworkUtils:
    """Network-related utility functions for BPF programs."""
    
    def __init__(self):
        """Initialize network utilities with caching."""
        self._cidr_cache: Dict[str, ipaddress.IPv4Network] = {}
    
    def ip_to_int(self, ip_str: str) -> int:
        """
        Convert IP address string to 32-bit integer.
        
        Args:
            ip_str: IP address in dotted decimal notation
            
        Returns:
            IP address as 32-bit integer (network byte order)
            
        Raises:
            IPAddressError: If IP address format is invalid
        """
        try:
            return struct.unpack("!I", socket.inet_aton(ip_str))[0]
        except socket.error as e:
            raise IPAddressError(f"Invalid IP address format: {ip_str}") from e
    
    def int_to_ip(self, ip_int: int) -> str:
        """
        Convert 32-bit integer to IP address string.
        
        Args:
            ip_int: IP address as 32-bit integer
            
        Returns:
            IP address in dotted decimal notation
            
        Raises:
            IPAddressError: If integer is not a valid IP address
        """
        try:
            if not (0 <= ip_int <= 0xFFFFFFFF):
                raise ValueError("IP integer out of range")
            return socket.inet_ntoa(struct.pack("!I", ip_int))
        except (struct.error, socket.error, ValueError) as e:
            raise IPAddressError(f"Invalid IP address integer: {ip_int}") from e
    
    def get_cidr_network(self, cidr_str: str) -> ipaddress.IPv4Network:
        """
        Get IPv4Network object for CIDR string with caching.
        
        Args:
            cidr_str: CIDR notation string (e.g., "***********/24")
            
        Returns:
            IPv4Network object
            
        Raises:
            CIDRError: If CIDR format is invalid
        """
        if cidr_str in self._cidr_cache:
            return self._cidr_cache[cidr_str]
        
        try:
            network = ipaddress.IPv4Network(cidr_str, strict=False)
            self._cidr_cache[cidr_str] = network
            return network
        except (ipaddress.AddressValueError, ipaddress.NetmaskValueError) as e:
            # Fallback: try to parse as single IP
            try:
                ip_part = cidr_str.split('/')[0]
                network = ipaddress.IPv4Network(f"{ip_part}/32", strict=False)
                self._cidr_cache[cidr_str] = network
                return network
            except Exception:
                raise CIDRError(f"Invalid CIDR format: {cidr_str}") from e
    
    def is_ip_in_cidr(self, ip_str: str, cidr_str: str) -> bool:
        """
        Check if IP address is within CIDR block.
        
        Args:
            ip_str: IP address to check
            cidr_str: CIDR block to check against
            
        Returns:
            True if IP is within CIDR block, False otherwise
            
        Raises:
            IPAddressError: If IP format is invalid
            CIDRError: If CIDR format is invalid
        """
        try:
            ip_addr = ipaddress.IPv4Address(ip_str)
            network = self.get_cidr_network(cidr_str)
            return ip_addr in network
        except (ipaddress.AddressValueError, ValueError) as e:
            raise IPAddressError(f"Invalid IP address: {ip_str}") from e
    
    def validate_ip_address(self, ip_str: str) -> bool:
        """
        Validate IP address format.
        
        Args:
            ip_str: IP address string to validate
            
        Returns:
            True if valid, False otherwise
        """
        try:
            ipaddress.IPv4Address(ip_str)
            return True
        except ipaddress.AddressValueError:
            return False
    
    def validate_port(self, port: Union[int, str]) -> bool:
        """
        Validate port number.
        
        Args:
            port: Port number to validate
            
        Returns:
            True if valid, False otherwise
        """
        try:
            port_num = int(port)
            return 1 <= port_num <= 65535
        except (ValueError, TypeError):
            return False


class StringUtils:
    """String utility functions for BPF programs."""

    @staticmethod
    def hash_string(s: str) -> int:
        """
        Calculate string hash value using djb2 algorithm.

        This method computes a hash value for strings, consistent with the
        BPF program implementation. Limited to first 16 characters for consistency.

        Args:
            s: String to hash

        Returns:
            32-bit hash value

        Raises:
            TypeError: If input is not a string
        """
        if not isinstance(s, str):
            raise TypeError("Input must be a string")

        hash_val = 5381
        for c in s[:16]:  # Limit to 16 characters for BPF compatibility
            hash_val = ((hash_val << 5) + hash_val + ord(c)) & 0xFFFFFFFF
        return hash_val

    @staticmethod
    def hash_long_string(s: str, max_len: int = 256) -> int:
        """
        Calculate string hash value for longer strings like file paths.

        This method computes a hash value for longer strings, consistent with the
        BPF program implementation for process paths.

        Args:
            s: String to hash
            max_len: Maximum length to consider for hashing

        Returns:
            32-bit hash value

        Raises:
            TypeError: If input is not a string
        """
        if not isinstance(s, str):
            raise TypeError("Input must be a string")

        hash_val = 5381
        for c in s[:max_len]:  # Limit to max_len characters
            hash_val = ((hash_val << 5) + hash_val + ord(c)) & 0xFFFFFFFF
        return hash_val
    
    @staticmethod
    def validate_process_name(process_name: str) -> bool:
        """
        Validate process name format.
        
        Args:
            process_name: Process name to validate
            
        Returns:
            True if valid, False otherwise
        """
        if not isinstance(process_name, str):
            return False
        
        # Check for empty or whitespace-only strings
        if not process_name.strip():
            return False
        
        # Check for reasonable length (BPF comm field is 16 chars)
        if len(process_name) > 255:  # Allow longer paths but warn
            logger.warning(f"Process name is very long: {len(process_name)} chars")
        
        return True


class ProcessUtils:
    """Process-related utility functions for BPF programs."""

    @staticmethod
    def get_process_path(pid: int) -> Optional[str]:
        """
        Get the executable path for a given process ID.

        Args:
            pid: Process ID

        Returns:
            Full path to the process executable, or None if not found
        """
        try:
            # Read the exe symlink from /proc/pid/exe
            exe_path = f"/proc/{pid}/exe"
            if os.path.exists(exe_path):
                return os.readlink(exe_path)
        except (OSError, FileNotFoundError, PermissionError):
            pass

        try:
            # Fallback: read from /proc/pid/cmdline
            cmdline_path = f"/proc/{pid}/cmdline"
            if os.path.exists(cmdline_path):
                with open(cmdline_path, 'r') as f:
                    cmdline = f.read().strip('\x00')
                    if cmdline:
                        # Take the first argument (executable path)
                        return cmdline.split('\x00')[0]
        except (OSError, FileNotFoundError, PermissionError):
            pass

        return None

    @staticmethod
    def get_all_process_paths() -> Dict[int, str]:
        """
        Get executable paths for all running processes.

        Returns:
            Dictionary mapping PID to executable path
        """
        process_paths = {}

        try:
            # Iterate through all PIDs in /proc
            for pid_str in os.listdir('/proc'):
                if pid_str.isdigit():
                    pid = int(pid_str)
                    path = ProcessUtils.get_process_path(pid)
                    if path:
                        process_paths[pid] = path
        except (OSError, PermissionError):
            logger.warning("Failed to read process information from /proc")

        return process_paths

    @staticmethod
    def validate_process_path(process_path: str) -> bool:
        """
        Validate process path format.

        Args:
            process_path: Process path to validate

        Returns:
            True if valid, False otherwise
        """
        if not isinstance(process_path, str):
            return False

        # Check for empty or whitespace-only strings
        if not process_path.strip():
            return False

        # Check if it looks like an absolute path
        if not process_path.startswith('/'):
            logger.warning(f"Process path should be absolute: {process_path}")

        # Check for reasonable length
        if len(process_path) > 4096:  # Linux PATH_MAX
            logger.warning(f"Process path is very long: {len(process_path)} chars")

        return True


class PolicyUtils:
    """Policy validation and management utilities."""
    
    @staticmethod
    def validate_policy_structure(config: Dict[str, Any]) -> None:
        """
        Validate the structure of policy configuration.
        
        Args:
            config: Loaded policy configuration
            
        Raises:
            PolicyError: If configuration structure is invalid
        """
        if not isinstance(config, dict):
            raise PolicyError("Configuration must be a dictionary")
        
        if 'ports' not in config:
            raise PolicyError("Configuration must contain 'ports' key")
        
        if not isinstance(config['ports'], list):
            raise PolicyError("'ports' must be a list")
        
        net_utils = NetworkUtils()
        
        for i, port_config in enumerate(config['ports']):
            if not isinstance(port_config, dict):
                raise PolicyError(f"Port configuration {i} must be a dictionary")
            
            if 'ports' not in port_config:
                raise PolicyError(f"Port configuration {i} must contain 'ports' key")
            
            # Validate port numbers
            for port in port_config['ports']:
                if not net_utils.validate_port(port):
                    raise PolicyError(f"Invalid port number in config {i}: {port}")
            
            # Validate CIDR formats if present
            cidr_config = port_config.get('cidr', {})
            for list_type in ['cidr_allow_list', 'cidr_deny_list']:
                cidr_list = cidr_config.get(list_type, [])
                for cidr in cidr_list:
                    try:
                        net_utils.get_cidr_network(cidr)
                    except CIDRError as e:
                        raise PolicyError(f"Invalid CIDR in {list_type}: {e}") from e
            
            # Validate process names if present
            process_config = port_config.get('process', {})
            for list_type in ['process_allow_list', 'process_deny_list']:
                process_list = process_config.get(list_type, [])
                for process in process_list:
                    if not StringUtils.validate_process_name(process):
                        raise PolicyError(f"Invalid process name in {list_type}: {process}")

            # Validate process paths if present
            for list_type in ['process_path_allow_list', 'process_path_deny_list']:
                process_path_list = process_config.get(list_type, [])
                for process_path in process_path_list:
                    if not ProcessUtils.validate_process_path(process_path):
                        raise PolicyError(f"Invalid process path in {list_type}: {process_path}")
    
    @staticmethod
    def load_yaml_config(file_path: str) -> Dict[str, Any]:
        """
        Safely load and validate YAML configuration file.
        
        Args:
            file_path: Path to YAML configuration file
            
        Returns:
            Loaded and validated configuration
            
        Raises:
            PolicyError: If file cannot be loaded or is invalid
        """
        try:
            if not os.path.exists(file_path):
                raise PolicyError(f"Policy file not found: {file_path}")
            
            with open(file_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            if config is None:
                raise PolicyError("Policy file is empty or invalid")
            
            # Validate structure
            PolicyUtils.validate_policy_structure(config)
            
            logger.info(f"Successfully loaded and validated policy from {file_path}")
            return config
            
        except yaml.YAMLError as e:
            raise PolicyError(f"YAML parsing error: {e}") from e
        except (IOError, OSError) as e:
            raise PolicyError(f"File I/O error: {e}") from e


class SystemUtils:
    """System-related utility functions."""
    
    @staticmethod
    def check_root_privileges() -> bool:
        """
        Check if the current process has root privileges.
        
        Returns:
            True if running as root, False otherwise
        """
        return os.geteuid() == 0
    
    @staticmethod
    def require_root_privileges(program_name: str = "program") -> None:
        """
        Require root privileges or exit with error message.
        
        Args:
            program_name: Name of the program for error message
            
        Raises:
            SystemExit: If not running as root
        """
        if not SystemUtils.check_root_privileges():
            print(f"Error: {program_name} requires root privileges")
            print(f"Please run with: sudo python3 {sys.argv[0]}")
            sys.exit(1)
    
    @staticmethod
    def setup_logging(level: int = logging.INFO, 
                     format_str: str = '%(asctime)s - %(name)s - %(levelname)s - %(message)s') -> None:
        """
        Setup consistent logging configuration.
        
        Args:
            level: Logging level
            format_str: Log message format string
        """
        logging.basicConfig(level=level, format=format_str)
        
        # Set BCC logging to WARNING to reduce noise
        logging.getLogger('bcc').setLevel(logging.WARNING)


# Create singleton instances for common use
network_utils = NetworkUtils()
string_utils = StringUtils()
process_utils = ProcessUtils()
policy_utils = PolicyUtils()
system_utils = SystemUtils()
