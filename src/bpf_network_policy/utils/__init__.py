"""
BPF Network Policy Utilities

This module provides shared utilities for BPF network policy management,
including IP address handling, CIDR parsing, string hashing, and common
validation functions.
"""

from .bpf_utils import (
    NetworkUtils,
    StringUtils,
    ProcessUtils,
    PolicyUtils,
    SystemUtils,
    network_utils,
    string_utils,
    process_utils,
    policy_utils,
    system_utils,
    BPFUtilsError,
    PolicyError,
    IPAddressError,
    CIDRError,
)

__all__ = [
    # Utility classes
    "NetworkUtils",
    "StringUtils",
    "ProcessUtils",
    "PolicyUtils",
    "SystemUtils",

    # Singleton instances
    "network_utils",
    "string_utils",
    "process_utils",
    "policy_utils",
    "system_utils",

    # Exceptions
    "BPFUtilsError",
    "PolicyError",
    "IPAddressError",
    "CIDRError",
]
