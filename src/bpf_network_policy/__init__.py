"""
BPF Network Policy Control System

A comprehensive eBPF-based network access control system that provides
fine-grained policy enforcement for network connections based on ports,
IP addresses, and processes.
"""

__version__ = "1.0.0"
__author__ = "BPF Policy Team"
__email__ = "<EMAIL>"
__description__ = "eBPF-based Network Policy Control System"

# Import main components for easy access
from .core.policy_control import BPFPolicyController
from .core.user_control import LegacyBPFController
from .utils.bpf_utils import (
    network_utils,
    string_utils,
    policy_utils,
    system_utils,
    BPFUtilsError,
    PolicyError,
    IPAddressError,
    CIDRError
)

__all__ = [
    # Version info
    "__version__",
    "__author__",
    "__email__",
    "__description__",
    
    # Core controllers
    "BPFPolicyController",
    "LegacyBPFController",
    
    # Utilities
    "network_utils",
    "string_utils", 
    "policy_utils",
    "system_utils",
    
    # Exceptions
    "BPFUtilsError",
    "PolicyError",
    "IPAddressError",
    "CIDRError",
]
