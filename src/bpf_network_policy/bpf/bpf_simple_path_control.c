// 简化版BPF路径策略控制程序
// 使用兼容性更好的方法，避免直接访问内核结构体

#include <uapi/linux/ptrace.h>
#include <net/sock.h>
#include <bcc/proto.h>

// 端口策略映射 - 每个端口对应一个策略ID
BPF_HASH(port_policy_map, u16, u32, 256);

// 进程名白名单 - key: policy_id << 32 | process_name_hash, value: 1
BPF_HASH(process_allow_map, u64, u8, 1024);

// 进程名黑名单 - key: policy_id << 32 | process_name_hash, value: 1  
BPF_HASH(process_deny_map, u64, u8, 1024);

// 进程路径白名单 - key: policy_id << 32 | process_path_hash, value: 1
// 注意：由于BCC限制，这里实际存储的是进程名的哈希，不是完整路径
BPF_HASH(process_path_allow_map, u64, u8, 2048);

// 进程路径黑名单 - key: policy_id << 32 | process_path_hash, value: 1
BPF_HASH(process_path_deny_map, u64, u8, 2048);

// IP白名单 - key: policy_id << 32 | ip_addr, value: 1
BPF_HASH(cidr_allow_map, u64, u8, 1024);

// IP黑名单 - key: policy_id << 32 | ip_addr, value: 1
BPF_HASH(cidr_deny_map, u64, u8, 1024);

// 统计信息
BPF_HASH(policy_stats, u32, u64, 10);

// 简单的字符串哈希函数
static inline u32 hash_string(char *str) {
    u32 hash = 5381;
    for (int i = 0; i < 16 && str[i] != 0; i++) {
        hash = ((hash << 5) + hash) + str[i];
    }
    return hash;
}

// 增强的字符串哈希函数，支持更长的路径
static inline u32 hash_long_string(char *str, int max_len) {
    u32 hash = 5381;
    for (int i = 0; i < max_len && str[i] != 0; i++) {
        hash = ((hash << 5) + hash) + str[i];
    }
    return hash;
}

// 获取进程信息的兼容性函数
static inline int get_process_info(char *path_buf, int buf_size) {
    // 由于BCC的限制，我们使用进程名作为"路径"
    // 在实际部署中，可以通过用户空间程序来维护PID到路径的映射
    char comm[16];
    int ret = bpf_get_current_comm(comm, sizeof(comm));
    if (ret != 0) {
        return -1;
    }
    
    // 将进程名复制到路径缓冲区
    int len = 0;
    for (int i = 0; i < 15 && i < buf_size - 1 && comm[i] != 0; i++) {
        path_buf[i] = comm[i];
        len++;
    }
    path_buf[len] = 0;
    
    return len;
}

// 主要的策略检查函数
int check_connection_policy(struct bpf_sock_addr *ctx) {
    // 获取连接信息
    u16 dst_port = bpf_ntohs(ctx->user_port);
    u32 src_ip = ctx->user_ip4;
    u32 pid = bpf_get_current_pid_tgid() >> 32;
    
    // 获取进程名称
    char comm[16];
    bpf_get_current_comm(&comm, sizeof(comm));
    u32 comm_hash = hash_string(comm);
    
    // 获取进程"路径"信息（实际上是进程名）
    char proc_info[64];
    u32 path_hash = 0;
    int path_ret = get_process_info(proc_info, sizeof(proc_info));
    if (path_ret >= 0) {
        path_hash = hash_long_string(proc_info, sizeof(proc_info));
    }
    
    // 更新总连接数统计
    u32 total_key = 0;
    u64 *total_count = policy_stats.lookup(&total_key);
    if (total_count) {
        (*total_count)++;
    } else {
        u64 init_val = 1;
        policy_stats.update(&total_key, &init_val);
    }
    
    // 1. 查找端口对应的策略ID
    u32 *policy_id_ptr = port_policy_map.lookup(&dst_port);
    if (!policy_id_ptr) {
        // 端口不在任何策略中，默认拒绝
        u32 denied_key = 2;
        u64 *denied_count = policy_stats.lookup(&denied_key);
        if (denied_count) {
            (*denied_count)++;
        } else {
            u64 init_val = 1;
            policy_stats.update(&denied_key, &init_val);
        }
        return 0;
    }
    
    u32 policy_id = *policy_id_ptr;
    
    // 2. 检查进程名黑名单
    u64 process_deny_key = ((u64)policy_id << 32) | comm_hash;
    u8 *process_denied = process_deny_map.lookup(&process_deny_key);
    if (process_denied && *process_denied == 1) {
        // 进程名在黑名单中，拒绝连接
        u32 denied_key = 2;
        u64 *denied_count = policy_stats.lookup(&denied_key);
        if (denied_count) {
            (*denied_count)++;
        } else {
            u64 init_val = 1;
            policy_stats.update(&denied_key, &init_val);
        }
        return 0;
    }
    
    // 2.1 检查进程路径黑名单（实际上是进程名的另一种哈希）
    if (path_hash != 0) {
        u64 process_path_deny_key = ((u64)policy_id << 32) | path_hash;
        u8 *process_path_denied = process_path_deny_map.lookup(&process_path_deny_key);
        if (process_path_denied && *process_path_denied == 1) {
            // 进程路径在黑名单中，拒绝连接
            u32 denied_key = 2;
            u64 *denied_count = policy_stats.lookup(&denied_key);
            if (denied_count) {
                (*denied_count)++;
            } else {
                u64 init_val = 1;
                policy_stats.update(&denied_key, &init_val);
            }
            return 0;
        }
    }
    
    // 3. 检查IP黑名单
    u64 ip_deny_key = ((u64)policy_id << 32) | src_ip;
    u8 *ip_denied = cidr_deny_map.lookup(&ip_deny_key);
    if (ip_denied && *ip_denied == 1) {
        // IP在黑名单中，拒绝连接
        u32 denied_key = 2;
        u64 *denied_count = policy_stats.lookup(&denied_key);
        if (denied_count) {
            (*denied_count)++;
        } else {
            u64 init_val = 1;
            policy_stats.update(&denied_key, &init_val);
        }
        return 0;
    }
    
    // 4. 检查进程名白名单
    u64 process_allow_key = ((u64)policy_id << 32) | comm_hash;
    u8 *process_allowed = process_allow_map.lookup(&process_allow_key);
    u8 process_in_whitelist = (process_allowed && *process_allowed == 1) ? 1 : 0;
    
    // 4.1 检查进程路径白名单
    u8 process_path_in_whitelist = 0;
    if (path_hash != 0) {
        u64 process_path_allow_key = ((u64)policy_id << 32) | path_hash;
        u8 *process_path_allowed = process_path_allow_map.lookup(&process_path_allow_key);
        process_path_in_whitelist = (process_path_allowed && *process_path_allowed == 1) ? 1 : 0;
    }
    
    // 5. 检查IP白名单
    u64 ip_allow_key = ((u64)policy_id << 32) | src_ip;
    u8 *ip_allowed = cidr_allow_map.lookup(&ip_allow_key);
    u8 ip_in_whitelist = (ip_allowed && *ip_allowed == 1) ? 1 : 0;

    // 6. 决策逻辑：(进程名在白名单 OR 进程路径在白名单) AND IP在白名单
    if ((process_in_whitelist || process_path_in_whitelist) && ip_in_whitelist) {
        // 允许连接
        u32 allowed_key = 1;
        u64 *allowed_count = policy_stats.lookup(&allowed_key);
        if (allowed_count) {
            (*allowed_count)++;
        } else {
            u64 init_val = 1;
            policy_stats.update(&allowed_key, &init_val);
        }
        return 1;
    } else {
        // 拒绝连接
        u32 denied_key = 2;
        u64 *denied_count = policy_stats.lookup(&denied_key);
        if (denied_count) {
            (*denied_count)++;
        } else {
            u64 init_val = 1;
            policy_stats.update(&denied_key, &init_val);
        }
        return 0;
    }
}

// 用于cgroup/connect4的入口点
int cgroup_connect4(struct bpf_sock_addr *ctx) {
    return check_connection_policy(ctx);
}

// 用于cgroup/connect6的入口点  
int cgroup_connect6(struct bpf_sock_addr *ctx) {
    return check_connection_policy(ctx);
}
