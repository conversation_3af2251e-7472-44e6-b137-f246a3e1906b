"""
BPF Programs

This module contains the eBPF C programs used for network policy enforcement.
The programs are loaded and compiled by the BCC framework.
"""

import os
from pathlib import Path

# Get the directory containing BPF programs
BPF_PROGRAMS_DIR = Path(__file__).parent

def get_bpf_program_path(program_name: str) -> str:
    """
    Get the full path to a BPF program file.
    
    Args:
        program_name: Name of the BPF program (e.g., 'bpf_policy_control.c')
        
    Returns:
        Full path to the BPF program file
        
    Raises:
        FileNotFoundError: If the BPF program file doesn't exist
    """
    program_path = BPF_PROGRAMS_DIR / program_name
    if not program_path.exists():
        raise FileNotFoundError(f"BPF program not found: {program_path}")
    return str(program_path)

# Available BPF programs
POLICY_CONTROL_PROGRAM = "bpf_policy_control.c"
LEGACY_CONTROL_PROGRAM = "bpf_control.c"
PATH_POLICY_CONTROL_PROGRAM = "bpf_path_policy_control.c"

__all__ = [
    "BPF_PROGRAMS_DIR",
    "get_bpf_program_path",
    "POLICY_CONTROL_PROGRAM",
    "LEGACY_CONTROL_PROGRAM",
    "PATH_POLICY_CONTROL_PROGRAM",
]
