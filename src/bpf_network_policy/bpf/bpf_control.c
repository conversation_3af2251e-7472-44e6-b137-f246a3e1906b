// BCC程序不需要vmlinux.h，BCC会自动处理内核头文件

// 定义端口白名单 Map
BPF_HASH(port_whitelist, u16, u8, 256);

// 定义 IP 白名单 Map
BPF_HASH(cidr_allow_list, u32, u8, 256);

// 定义 IP 黑名单 Map
BPF_HASH(cidr_deny_list, u32, u8, 256);

// 定义进程白名单 Map
BPF_HASH(process_allow_list, u32, u8, 256);

// 定义进程黑名单 Map
BPF_HASH(process_deny_list, u32, u8, 256);

// 主程序，用于检查端口和 IP 的访问控制
int enforce_port_policy(struct bpf_sock_addr *ctx) {
    // 1. 获取目标端口和源 IP
    u16 target_port = bpf_ntohs(ctx->user_port); // 将用户端口转为主机字节序
    u32 src_ip = ctx->user_ip4;                 // 获取 IPv4 源地址

    // 2. 检查端口白名单
    u8 *allowed_port = port_whitelist.lookup(&target_port);
    if (!allowed_port || *allowed_port == 0) { // 如果端口不被允许，返回禁止访问
        return 0;
    }

    // 3. 检查源 IP 是否在白名单中
    u8 *allowed_cidr = cidr_allow_list.lookup(&src_ip);
    if (allowed_cidr && *allowed_cidr == 1) {  // 如果 IP 在白名单中，允许访问
        return 1;
    }

    // 4. 检查源 IP 是否在黑名单中
    u8 *deny_cidr = cidr_deny_list.lookup(&src_ip);
    if (deny_cidr && *deny_cidr == 1) {        // 如果 IP 在黑名单中，禁止访问
        return 0;
    }

    // 5. 检查进程是否在禁止列表中
    u32 pid = bpf_get_current_pid_tgid() >> 32;                // 获取当前进程 PID
    u8 *proc_status = process_deny_list.lookup(&pid);
    if (proc_status && *proc_status == 1) {    // 如果进程在禁止列表中，禁止访问
        return 0;
    }

    // 6. 检查进程是否在允许列表中
    proc_status = process_allow_list.lookup(&pid);
    if (proc_status && *proc_status == 1) {    // 如果进程在允许列表中，允许访问
        return 1;
    }

    // 7. 默认允许
    return 1;
}

// BCC会自动处理License