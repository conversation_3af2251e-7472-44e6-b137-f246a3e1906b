// 最小化BPF测试程序
// 用于验证基本的BPF功能和哈希计算

#include <uapi/linux/ptrace.h>
#include <uapi/linux/bpf.h>

// 端口策略映射
BPF_HASH(port_policy_map, u16, u32, 256);

// 进程名白名单
BPF_HASH(process_allow_map, u64, u8, 1024);

// 进程名黑名单  
BPF_HASH(process_deny_map, u64, u8, 1024);

// 统计信息
BPF_HASH(policy_stats, u32, u64, 10);

// 简单的字符串哈希函数
static inline u32 hash_string(char *str) {
    u32 hash = 5381;
    for (int i = 0; i < 16 && str[i] != 0; i++) {
        hash = ((hash << 5) + hash) + str[i];
    }
    return hash;
}

// 增强的字符串哈希函数
static inline u32 hash_long_string(char *str, int max_len) {
    u32 hash = 5381;
    for (int i = 0; i < max_len && str[i] != 0; i++) {
        hash = ((hash << 5) + hash) + str[i];
    }
    return hash;
}

// 简化的策略检查函数
int check_simple_policy(void *ctx) {
    // 获取进程名称
    char comm[16];
    int ret = bpf_get_current_comm(&comm, sizeof(comm));
    if (ret != 0) {
        return 0;
    }
    
    // 计算进程名哈希
    u32 comm_hash = hash_string(comm);
    
    // 更新统计信息
    u32 total_key = 0;
    u64 *total_count = policy_stats.lookup(&total_key);
    if (total_count) {
        (*total_count)++;
    } else {
        u64 init_val = 1;
        policy_stats.update(&total_key, &init_val);
    }
    
    // 简单的策略检查：假设策略ID为1
    u32 policy_id = 1;
    u64 process_key = ((u64)policy_id << 32) | comm_hash;
    
    // 检查黑名单
    u8 *denied = process_deny_map.lookup(&process_key);
    if (denied && *denied == 1) {
        // 更新拒绝统计
        u32 denied_key = 2;
        u64 *denied_count = policy_stats.lookup(&denied_key);
        if (denied_count) {
            (*denied_count)++;
        } else {
            u64 init_val = 1;
            policy_stats.update(&denied_key, &init_val);
        }
        return 0;  // 拒绝
    }
    
    // 检查白名单
    u8 *allowed = process_allow_map.lookup(&process_key);
    if (allowed && *allowed == 1) {
        // 更新允许统计
        u32 allowed_key = 1;
        u64 *allowed_count = policy_stats.lookup(&allowed_key);
        if (allowed_count) {
            (*allowed_count)++;
        } else {
            u64 init_val = 1;
            policy_stats.update(&allowed_key, &init_val);
        }
        return 1;  // 允许
    }
    
    // 默认拒绝
    u32 denied_key = 2;
    u64 *denied_count = policy_stats.lookup(&denied_key);
    if (denied_count) {
        (*denied_count)++;
    } else {
        u64 init_val = 1;
        policy_stats.update(&denied_key, &init_val);
    }
    return 0;
}

// 测试哈希函数的辅助函数
int test_hash_function(void *ctx) {
    char test_str[] = "test_string";
    u32 hash_result = hash_string(test_str);
    
    // 将哈希结果存储到统计Map中用于验证
    u32 hash_key = 100;
    u64 hash_val = (u64)hash_result;
    policy_stats.update(&hash_key, &hash_val);
    
    return hash_result;
}

// 测试长字符串哈希的辅助函数
int test_long_hash_function(void *ctx) {
    char test_str[] = "/usr/bin/test_program";
    u32 hash_result = hash_long_string(test_str, sizeof(test_str));
    
    // 将哈希结果存储到统计Map中用于验证
    u32 hash_key = 101;
    u64 hash_val = (u64)hash_result;
    policy_stats.update(&hash_key, &hash_val);
    
    return hash_result;
}
