#!/usr/bin/env python3
"""
简单的eBPF拦截测试
测试eBPF是否能够基本工作并拦截网络连接
"""

import os
import sys
import time
import socket
import subprocess
from bcc import BPF

def test_basic_ebpf():
    """测试基本的eBPF功能"""
    print("=== 测试基本eBPF功能 ===")
    
    # 检查权限
    if os.geteuid() != 0:
        print("❌ 需要root权限")
        return False
    
    try:
        # 最简单的eBPF程序
        bpf_code = """
#include <uapi/linux/ptrace.h>
#include <linux/socket.h>

BPF_HASH(connection_count, u32, u64, 10);

int trace_connect(struct bpf_sock_addr *ctx) {
    u32 key = 0;
    u64 *count = connection_count.lookup(&key);
    if (count) {
        (*count)++;
    } else {
        u64 init_val = 1;
        connection_count.update(&key, &init_val);
    }
    
    // 获取目标端口
    u16 port = bpf_ntohs(ctx->user_port);
    
    // 简单的拦截逻辑：拒绝连接到8080端口
    if (port == 8080) {
        return 0; // 拒绝连接
    }
    
    return 1; // 允许其他连接
}
"""
        
        print("编译eBPF程序...")
        bpf = BPF(text=bpf_code)
        print("✓ eBPF程序编译成功")
        
        # 加载函数
        fn = bpf.load_func("trace_connect", BPF.CGROUP_SOCK_ADDR)
        print("✓ eBPF函数加载成功")
        
        # 创建测试cgroup
        test_cgroup = "/sys/fs/cgroup/test_simple_ebpf"
        if not os.path.exists(test_cgroup):
            os.makedirs(test_cgroup)
        print(f"✓ 创建测试cgroup: {test_cgroup}")
        
        # 将当前进程加入cgroup
        with open(f"{test_cgroup}/cgroup.procs", "w") as f:
            f.write(str(os.getpid()))
        print(f"✓ 进程 {os.getpid()} 加入cgroup")
        
        # 附加eBPF程序
        bpf.attach_func(fn, test_cgroup, BPF.CGROUP_INET4_CONNECT)
        print("✓ eBPF程序附加到cgroup成功")
        
        # 测试连接
        print("\n测试网络连接...")
        
        # 测试允许的连接
        print("测试允许的连接 (端口80):")
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(2)
            result = sock.connect_ex(("127.0.0.1", 80))
            sock.close()
            print(f"  连接127.0.0.1:80 - {'成功' if result == 0 else '失败'}")
        except Exception as e:
            print(f"  连接127.0.0.1:80 - 异常: {e}")
        
        # 测试被拒绝的连接
        print("测试被拒绝的连接 (端口8080):")
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(2)
            result = sock.connect_ex(("127.0.0.1", 8080))
            sock.close()
            print(f"  连接127.0.0.1:8080 - {'成功' if result == 0 else '失败'} (应该失败)")
        except Exception as e:
            print(f"  连接127.0.0.1:8080 - 异常: {e}")
        
        # 检查统计信息
        time.sleep(1)
        connection_count = bpf.get_table("connection_count")
        count = connection_count[0].value if 0 in connection_count else 0
        print(f"\n✓ eBPF统计到 {count} 次连接尝试")
        
        # 清理
        bpf.detach_func(fn, test_cgroup, BPF.CGROUP_INET4_CONNECT)
        print("✓ eBPF程序已分离")
        
        try:
            os.rmdir(test_cgroup)
            print("✓ 清理测试cgroup")
        except:
            print("⚠️  无法清理测试cgroup")
        
        return count > 0
        
    except Exception as e:
        print(f"❌ 基本eBPF测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_with_existing_bpf():
    """使用项目中现有的BPF程序进行测试"""
    print("\n=== 测试现有BPF程序 ===")
    
    try:
        # 使用项目中的BPF程序
        bpf_file = "src/bpf_network_policy/bpf/bpf_minimal_test.c"
        if not os.path.exists(bpf_file):
            print(f"❌ BPF文件不存在: {bpf_file}")
            return False
        
        print(f"加载BPF程序: {bpf_file}")
        bpf = BPF(src_file=bpf_file)
        print("✓ BPF程序加载成功")
        
        # 检查是否有可用的函数
        print("可用的BPF函数:")
        for func_name in bpf.get_syscall_fnname("connect"):
            print(f"  - {func_name}")
        
        return True
        
    except Exception as e:
        print(f"❌ 现有BPF程序测试失败: {e}")
        return False

def main():
    """主函数"""
    print("简单eBPF拦截测试")
    print("=" * 40)
    
    success = True
    
    # 测试基本eBPF功能
    if not test_basic_ebpf():
        success = False
    
    # 测试现有BPF程序
    if not test_with_existing_bpf():
        success = False
    
    if success:
        print("\n🎉 eBPF基本功能测试成功！")
        print("✓ eBPF程序能够编译和加载")
        print("✓ eBPF程序能够附加到cgroup")
        print("✓ eBPF程序能够统计网络连接")
    else:
        print("\n❌ eBPF基本功能测试失败")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
