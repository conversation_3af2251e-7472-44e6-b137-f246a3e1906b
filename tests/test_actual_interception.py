#!/usr/bin/env python3
"""
实际策略拦截测试

测试BPF程序是否能真正拦截网络连接：
1. 加载BPF程序到cgroup
2. 配置策略规则
3. 尝试网络连接
4. 验证拦截效果
"""

import os
import sys
import time
import socket
import subprocess
import threading
from typing import Dict, List, Optional

# Add the project root to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from bcc import BPF
from src.bpf_network_policy.utils import (
    StringUtils, SystemUtils, string_utils, system_utils
)

class InterceptionTester:
    """实际拦截测试器"""
    
    def __init__(self):
        self.bpf = None
        self.cgroup_path = "/sys/fs/cgroup/test_bpf_policy"
        self.test_results = []
        
    def setup_cgroup(self):
        """设置测试用的cgroup"""
        print("=== 设置测试环境 ===")
        
        try:
            # 检查是否有root权限
            if os.geteuid() != 0:
                print("❌ 需要root权限来设置cgroup和加载BPF程序")
                return False
            
            # 创建测试cgroup
            if not os.path.exists(self.cgroup_path):
                os.makedirs(self.cgroup_path)
                print(f"✓ 创建测试cgroup: {self.cgroup_path}")
            else:
                print(f"✓ 使用现有cgroup: {self.cgroup_path}")
            
            # 将当前进程加入cgroup
            with open(f"{self.cgroup_path}/cgroup.procs", "w") as f:
                f.write(str(os.getpid()))
            print(f"✓ 将进程 {os.getpid()} 加入cgroup")
            
            return True
            
        except Exception as e:
            print(f"❌ 设置cgroup失败: {e}")
            return False
    
    def load_bpf_program(self):
        """加载BPF程序"""
        print("\n=== 加载BPF程序 ===")
        
        try:
            # 使用简化的BPF程序
            bpf_code = """
#include <uapi/linux/ptrace.h>
#include <uapi/linux/bpf.h>
#include <linux/socket.h>

// 端口策略映射
BPF_HASH(port_policy_map, u16, u32, 256);

// 进程名白名单
BPF_HASH(process_allow_map, u64, u8, 1024);

// 进程名黑名单  
BPF_HASH(process_deny_map, u64, u8, 1024);

// IP白名单
BPF_HASH(ip_allow_map, u64, u8, 1024);

// 统计信息
BPF_HASH(policy_stats, u32, u64, 10);

// 简单的字符串哈希函数
static inline u32 hash_string(char *str) {
    u32 hash = 5381;
    for (int i = 0; i < 16 && str[i] != 0; i++) {
        hash = ((hash << 5) + hash) + str[i];
    }
    return hash;
}

// 策略检查函数
int check_connect_policy(struct bpf_sock_addr *ctx) {
    // 获取连接信息
    u16 dst_port = bpf_ntohs(ctx->user_port);
    u32 dst_ip = ctx->user_ip4;
    
    // 获取进程名称
    char comm[16];
    int ret = bpf_get_current_comm(&comm, sizeof(comm));
    if (ret != 0) {
        return 0; // 获取进程名失败，拒绝连接
    }
    
    u32 comm_hash = hash_string(comm);
    
    // 更新总连接数统计
    u32 total_key = 0;
    u64 *total_count = policy_stats.lookup(&total_key);
    if (total_count) {
        (*total_count)++;
    } else {
        u64 init_val = 1;
        policy_stats.update(&total_key, &init_val);
    }
    
    // 1. 查找端口对应的策略ID
    u32 *policy_id_ptr = port_policy_map.lookup(&dst_port);
    if (!policy_id_ptr) {
        // 端口不在任何策略中，默认允许（测试用）
        return 1;
    }
    
    u32 policy_id = *policy_id_ptr;
    
    // 2. 检查进程黑名单
    u64 process_deny_key = ((u64)policy_id << 32) | comm_hash;
    u8 *process_denied = process_deny_map.lookup(&process_deny_key);
    if (process_denied && *process_denied == 1) {
        // 进程在黑名单中，拒绝连接
        u32 denied_key = 2;
        u64 *denied_count = policy_stats.lookup(&denied_key);
        if (denied_count) {
            (*denied_count)++;
        } else {
            u64 init_val = 1;
            policy_stats.update(&denied_key, &init_val);
        }
        return 0; // 拒绝
    }
    
    // 3. 检查IP白名单
    u64 ip_allow_key = ((u64)policy_id << 32) | dst_ip;
    u8 *ip_allowed = ip_allow_map.lookup(&ip_allow_key);
    if (!ip_allowed || *ip_allowed != 1) {
        // IP不在白名单中，拒绝连接
        u32 denied_key = 2;
        u64 *denied_count = policy_stats.lookup(&denied_key);
        if (denied_count) {
            (*denied_count)++;
        } else {
            u64 init_val = 1;
            policy_stats.update(&denied_key, &init_val);
        }
        return 0; // 拒绝
    }
    
    // 4. 检查进程白名单
    u64 process_allow_key = ((u64)policy_id << 32) | comm_hash;
    u8 *process_allowed = process_allow_map.lookup(&process_allow_key);
    if (process_allowed && *process_allowed == 1) {
        // 允许连接
        u32 allowed_key = 1;
        u64 *allowed_count = policy_stats.lookup(&allowed_key);
        if (allowed_count) {
            (*allowed_count)++;
        } else {
            u64 init_val = 1;
            policy_stats.update(&allowed_key, &init_val);
        }
        return 1; // 允许
    }
    
    // 默认拒绝
    u32 denied_key = 2;
    u64 *denied_count = policy_stats.lookup(&denied_key);
    if (denied_count) {
        (*denied_count)++;
    } else {
        u64 init_val = 1;
        policy_stats.update(&denied_key, &init_val);
    }
    return 0; // 拒绝
}
"""
            
            # 编译BPF程序
            self.bpf = BPF(text=bpf_code)
            print("✓ BPF程序编译成功")
            
            # 获取BPF函数
            fn = self.bpf.load_func("check_connect_policy", BPF.CGROUP_SOCK_ADDR)
            print("✓ BPF函数加载成功")
            
            # 附加到cgroup
            self.bpf.attach_func(fn, self.cgroup_path, BPF.CGROUP_INET4_CONNECT)
            print(f"✓ BPF程序附加到cgroup: {self.cgroup_path}")
            
            return True
            
        except Exception as e:
            print(f"❌ 加载BPF程序失败: {e}")
            return False
    
    def configure_policies(self):
        """配置测试策略"""
        print("\n=== 配置测试策略 ===")
        
        if not self.bpf:
            print("❌ BPF程序未加载")
            return False
        
        try:
            # 配置端口策略映射
            port_policy_map = self.bpf.get_table("port_policy_map")
            port_policy_map[80] = 1   # HTTP端口对应策略ID 1
            port_policy_map[443] = 1  # HTTPS端口对应策略ID 1
            port_policy_map[8080] = 2 # 测试端口对应策略ID 2
            print("✓ 配置端口策略映射")
            
            # 配置进程白名单（策略ID 1）
            process_allow_map = self.bpf.get_table("process_allow_map")
            policy_id = 1
            
            # 允许python3访问HTTP/HTTPS
            python_hash = string_utils.hash_string("python3")
            python_key = (policy_id << 32) | python_hash
            process_allow_map[python_key] = 1
            print(f"✓ 添加进程白名单: python3 (哈希: {python_hash})")
            
            # 配置进程黑名单（策略ID 2）
            process_deny_map = self.bpf.get_table("process_deny_map")
            policy_id = 2
            
            # 禁止python3访问8080端口
            python_deny_key = (policy_id << 32) | python_hash
            process_deny_map[python_deny_key] = 1
            print(f"✓ 添加进程黑名单: python3 for port 8080")
            
            # 配置IP白名单
            ip_allow_map = self.bpf.get_table("ip_allow_map")
            
            # 允许访问本地和一些公共服务
            allowed_ips = [
                "127.0.0.1",    # 本地
                "*******",      # Google DNS
                "*******",      # Cloudflare DNS
            ]
            
            for ip_str in allowed_ips:
                ip_parts = ip_str.split('.')
                ip_int = (int(ip_parts[0]) << 24) | (int(ip_parts[1]) << 16) | \
                        (int(ip_parts[2]) << 8) | int(ip_parts[3])
                
                # 为策略ID 1添加IP白名单
                ip_key_1 = (1 << 32) | ip_int
                ip_allow_map[ip_key_1] = 1
                
                print(f"✓ 添加IP白名单: {ip_str} (策略1)")
            
            return True
            
        except Exception as e:
            print(f"❌ 配置策略失败: {e}")
            return False
    
    def test_connection(self, host: str, port: int, should_succeed: bool) -> bool:
        """测试网络连接"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)  # 5秒超时
            
            start_time = time.time()
            result = sock.connect_ex((host, port))
            end_time = time.time()
            
            sock.close()
            
            success = (result == 0)
            duration = end_time - start_time
            
            if success == should_succeed:
                status = "✓ 符合预期"
            else:
                status = "❌ 不符合预期"
            
            result_str = "成功" if success else "失败"
            expect_str = "成功" if should_succeed else "失败"
            
            print(f"  连接 {host}:{port} - {result_str} (预期: {expect_str}) - {duration:.2f}s - {status}")
            
            return success == should_succeed
            
        except Exception as e:
            print(f"  连接 {host}:{port} - 异常: {e}")
            return not should_succeed  # 异常通常意味着连接失败
    
    def test_interception_scenarios(self):
        """测试拦截场景"""
        print("\n=== 测试拦截场景 ===")
        
        if not self.bpf:
            print("❌ BPF程序未加载")
            return False
        
        # 测试场景
        test_cases = [
            # (host, port, should_succeed, description)
            ("127.0.0.1", 80, True, "本地HTTP - 应该允许"),
            ("*******", 80, True, "Google DNS HTTP - 应该允许"),
            ("*******", 80, True, "Cloudflare HTTP - 应该允许"),
            ("127.0.0.1", 8080, False, "本地8080 - 应该拒绝"),
            ("*******", 8080, False, "Google DNS 8080 - 应该拒绝"),
        ]
        
        print(f"当前进程: {os.getpid()}")
        print(f"进程名: python3")
        print(f"进程哈希: {string_utils.hash_string('python3')}")
        
        passed = 0
        total = len(test_cases)
        
        for host, port, should_succeed, description in test_cases:
            print(f"\n测试: {description}")
            if self.test_connection(host, port, should_succeed):
                passed += 1
        
        print(f"\n连接测试结果: {passed}/{total} 通过")
        return passed == total
    
    def check_statistics(self):
        """检查统计信息"""
        print("\n=== 检查统计信息 ===")
        
        if not self.bpf:
            print("❌ BPF程序未加载")
            return False
        
        try:
            policy_stats = self.bpf.get_table("policy_stats")
            
            # 读取统计数据
            stats = {}
            for key, value in policy_stats.items():
                stats[key.value] = value.value
            
            total_connections = stats.get(0, 0)
            allowed_connections = stats.get(1, 0)
            denied_connections = stats.get(2, 0)
            
            print(f"统计信息:")
            print(f"  总连接尝试: {total_connections}")
            print(f"  允许连接: {allowed_connections}")
            print(f"  拒绝连接: {denied_connections}")
            
            if total_connections > 0:
                print("✓ BPF程序正在处理连接")
                return True
            else:
                print("⚠️  没有检测到连接尝试")
                return False
                
        except Exception as e:
            print(f"❌ 检查统计信息失败: {e}")
            return False
    
    def cleanup(self):
        """清理测试环境"""
        print("\n=== 清理测试环境 ===")
        
        try:
            if self.bpf:
                # BCC会自动清理BPF程序
                print("✓ BPF程序已清理")
            
            # 移除测试cgroup（可选）
            # 注意：不要删除系统cgroup
            if self.cgroup_path.startswith("/sys/fs/cgroup/test_"):
                try:
                    os.rmdir(self.cgroup_path)
                    print(f"✓ 删除测试cgroup: {self.cgroup_path}")
                except:
                    print(f"⚠️  无法删除cgroup: {self.cgroup_path}")
            
        except Exception as e:
            print(f"⚠️  清理过程中出现问题: {e}")
    
    def run_full_test(self):
        """运行完整测试"""
        print("BPF网络策略实际拦截测试")
        print("=" * 50)
        
        # 检查权限
        if os.geteuid() != 0:
            print("❌ 此测试需要root权限")
            print("请使用: sudo python3 tests/test_actual_interception.py")
            return False
        
        success = True
        
        try:
            # 1. 设置测试环境
            if not self.setup_cgroup():
                return False
            
            # 2. 加载BPF程序
            if not self.load_bpf_program():
                return False
            
            # 3. 配置策略
            if not self.configure_policies():
                return False
            
            # 4. 测试拦截
            if not self.test_interception_scenarios():
                success = False
            
            # 5. 检查统计
            if not self.check_statistics():
                success = False
            
        except KeyboardInterrupt:
            print("\n⚠️  测试被用户中断")
            success = False
        except Exception as e:
            print(f"\n❌ 测试过程中出现异常: {e}")
            success = False
        finally:
            # 6. 清理环境
            self.cleanup()
        
        if success:
            print("\n🎉 实际拦截测试通过！")
            print("BPF程序成功拦截了网络连接")
        else:
            print("\n❌ 实际拦截测试失败")
        
        return success

def main():
    """主函数"""
    tester = InterceptionTester()
    success = tester.run_full_test()
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
