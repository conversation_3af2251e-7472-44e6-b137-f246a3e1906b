#!/usr/bin/env python3
"""
Basic BPF Interception Functionality Demonstration

This module demonstrates basic BPF interception capabilities using
simple kprobe monitoring of system calls. It provides a testing
framework for network connection monitoring and policy enforcement.
"""

from bcc import BPF
import socket
import time
import os
import sys
import threading
from typing import List, Dict, Tuple, Optional, Any
import logging

# Import shared utilities
from bpf_network_policy.utils.bpf_utils import (
    network_utils, system_utils, logger
)

# Configure logging for this module
logger = logging.getLogger(__name__)

# 简化的BPF程序
BPF_PROGRAM = """
#include <linux/sched.h>

// 端口白名单
BPF_HASH(port_whitelist, u16, u8, 256);

// 连接统计
BPF_HASH(connection_stats, u32, u64, 10);

struct connect_event {
    u32 pid;
    u16 port;
    char comm[16];
    u8 allowed;
};

BPF_PERF_OUTPUT(connect_events);

// 监控connect系统调用
int trace_connect_entry(struct pt_regs *ctx) {
    u32 pid = bpf_get_current_pid_tgid() >> 32;
    
    // 简单的演示：假设连接到端口80
    u16 test_port = 80;
    
    // 检查端口白名单
    u8 *allowed = port_whitelist.lookup(&test_port);
    u8 is_allowed = (allowed && *allowed == 1) ? 1 : 0;
    
    // 更新统计
    u32 stat_key = is_allowed ? 1 : 2;  // 1=allowed, 2=blocked
    u64 *count = connection_stats.lookup(&stat_key);
    if (count) {
        (*count)++;
    } else {
        u64 init_val = 1;
        connection_stats.update(&stat_key, &init_val);
    }
    
    // 发送事件
    struct connect_event event = {};
    event.pid = pid;
    event.port = test_port;
    event.allowed = is_allowed;
    bpf_get_current_comm(&event.comm, sizeof(event.comm));
    
    connect_events.perf_submit(ctx, &event, sizeof(event));
    
    return 0;
}
"""

class BasicInterceptTest:
    """
    Basic BPF interception test framework.

    This class provides a testing framework for demonstrating BPF-based
    network connection monitoring and policy enforcement capabilities.
    """

    def __init__(self) -> None:
        """Initialize the basic intercept test framework."""
        self.bpf: Optional[BPF] = None
        self.events: List[Dict[str, Any]] = []
        self._monitoring = False

    def load_bpf_program(self) -> bool:
        """
        Load and compile the BPF monitoring program.

        Returns:
            True if BPF program loaded successfully, False otherwise
        """
        try:
            print("1. Loading basic BPF monitoring program...")
            logger.info("Loading BPF monitoring program")

            self.bpf = BPF(text=BPF_PROGRAM)

            print("   ✓ BPF program loaded successfully")
            logger.info("BPF monitoring program compiled and loaded successfully")
            return True

        except Exception as e:
            error_msg = f"BPF program loading failed: {e}"
            print(f"   ✗ {error_msg}")
            logger.error(error_msg, exc_info=True)
            return False

    def configure_policy(self) -> bool:
        """
        Configure test policy rules.

        Returns:
            True if policies configured successfully, False otherwise
        """
        try:
            if not self.bpf:
                raise RuntimeError("BPF program not loaded")

            print("2. Configuring port policies...")
            logger.info("Configuring test port policies")

            # Configure port whitelist
            try:
                port_table = self.bpf.get_table("port_whitelist")
            except KeyError as e:
                raise RuntimeError(f"BPF table not found: {e}")

            # Allow specific ports for testing
            allowed_ports = [22, 53, 8080]  # SSH, DNS, test port

            for port in allowed_ports:
                if not network_utils.validate_port(port):
                    logger.warning(f"Invalid port number: {port}")
                    continue
                port_table[port_table.Key(port)] = port_table.Leaf(1)

            print(f"   ✓ Port whitelist configured: {allowed_ports}")
            print("   ✗ Port 80 intentionally excluded for interception demonstration")
            logger.info(f"Configured port whitelist: {allowed_ports}")
            return True

        except Exception as e:
            error_msg = f"Policy configuration failed: {e}"
            print(f"   ✗ {error_msg}")
            logger.error(error_msg, exc_info=True)
            return False
    
    def attach_probe(self):
        """附加探针"""
        try:
            print("3. 附加系统调用监控...")
            self.bpf.attach_kprobe(event="__sys_connect", fn_name="trace_connect_entry")
            print("   ✓ 已附加到connect系统调用")
            return True
        except Exception as e:
            print(f"   ✗ 附加探针失败: {e}")
            try:
                # 尝试其他可能的函数名
                self.bpf.attach_kprobe(event="sys_connect", fn_name="trace_connect_entry")
                print("   ✓ 已附加到sys_connect")
                return True
            except Exception as e2:
                print(f"   ✗ 备用附加也失败: {e2}")
                return False
    
    def event_handler(self, cpu, data, size):
        """处理事件"""
        event = self.bpf["connect_events"].event(data)
        
        action_str = "✓ 允许" if event.allowed == 1 else "✗ 拦截"
        comm_str = event.comm.decode('utf-8', 'replace')
        
        print(f"   {action_str} | {comm_str}(PID:{event.pid}) -> 端口:{event.port}")
        
        self.events.append({
            'pid': event.pid,
            'comm': comm_str,
            'port': event.port,
            'allowed': event.allowed == 1
        })
    
    def start_monitoring(self):
        """开始监控"""
        print("4. 开始监控连接...")
        self.bpf["connect_events"].open_perf_buffer(self.event_handler)
    
    def generate_test_activity(self):
        """生成测试活动"""
        print("\n5. 生成测试网络活动...")
        
        # 尝试一些网络连接来触发系统调用
        test_hosts = [
            ("127.0.0.1", 22),
            ("127.0.0.1", 80),
            ("127.0.0.1", 443),
        ]
        
        for host, port in test_hosts:
            print(f"   尝试连接: {host}:{port}")
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(0.5)
                sock.connect_ex((host, port))
                sock.close()
            except:
                pass
            time.sleep(0.2)
    
    def show_statistics(self):
        """显示统计信息"""
        try:
            stats_table = self.bpf.get_table("connection_stats")
            
            print("\n=== 连接统计 ===")
            try:
                allowed = stats_table[stats_table.Key(1)].value
                print(f"允许的连接: {allowed}")
            except:
                print("允许的连接: 0")
            
            try:
                blocked = stats_table[stats_table.Key(2)].value
                print(f"拦截的连接: {blocked}")
            except:
                print("拦截的连接: 0")
                
        except Exception as e:
            print(f"获取统计信息失败: {e}")
    
    def run_test(self):
        """运行测试"""
        print("=== BPF基础拦截监控测试 ===\n")
        print("注意: 这是一个简化的演示程序")
        print("实际的网络拦截需要更复杂的内核集成\n")
        
        try:
            if not self.load_bpf_program():
                return False
            
            if not self.configure_policy():
                return False
            
            if not self.attach_probe():
                return False
            
            self.start_monitoring()
            
            # 在后台生成测试活动
            def test_thread():
                time.sleep(1)
                self.generate_test_activity()
            
            thread = threading.Thread(target=test_thread)
            thread.start()
            
            # 监控5秒
            print("   监控系统调用中...")
            start_time = time.time()
            while time.time() - start_time < 5:
                self.bpf.perf_buffer_poll(timeout=100)
            
            thread.join()
            
            self.show_statistics()
            
            print(f"\n✓ 监控完成，共捕获 {len(self.events)} 个事件")
            
            # 显示配置的策略
            print("\n=== 配置的策略 ===")
            port_table = self.bpf.get_table("port_whitelist")
            print("端口白名单:")
            for k, v in port_table.items():
                if v.value == 1:
                    print(f"  - 端口 {k.value}")
            
            return True
            
        except KeyboardInterrupt:
            print("\n测试被用户中断")
            return False
        except Exception as e:
            print(f"测试失败: {e}")
            return False

def main() -> None:
    """
    Main entry point for the basic BPF interception test.

    This function sets up logging, checks privileges, and runs
    the BPF interception demonstration.
    """
    try:
        # Setup logging
        system_utils.setup_logging()

        # Check root privileges
        system_utils.require_root_privileges("Basic BPF Interception Test")

        # Create and run test
        tester = BasicInterceptTest()
        success = tester.run_test()

        # Exit with appropriate status code
        exit_code = 0 if success else 1
        logger.info(f"Test completed with exit code: {exit_code}")
        sys.exit(exit_code)

    except KeyboardInterrupt:
        print("\nTest interrupted by user")
        logger.info("Test interrupted by user")
        sys.exit(130)  # Standard exit code for SIGINT
    except Exception as e:
        error_msg = f"Fatal error: {e}"
        print(f"✗ {error_msg}")
        logger.error(error_msg, exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    main()
