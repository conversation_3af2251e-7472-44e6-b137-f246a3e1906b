#!/usr/bin/env python3
"""
Simplified YAML Policy Testing Program

This module provides a comprehensive YAML policy validator for network access control.
It validates connections based on process names, IP addresses, and ports using
allow/deny lists for both processes and CIDR blocks.
"""
import yaml
import socket
import struct
import ipaddress
import os
import sys
from typing import Dict, List, Tuple, Optional, Union, Any
import logging

# Import shared utilities
from bpf_network_policy.utils.bpf_utils import (
    network_utils, string_utils, policy_utils, system_utils,
    BPFUtilsError, PolicyError, logger
)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class PolicyValidationError(Exception):
    """Custom exception for policy validation errors."""
    pass


class YAMLPolicyValidator:
    """
    A comprehensive YAML policy validator for network access control.

    This class loads network access policies from YAML configuration files
    and validates connections based on process names, IP addresses, and ports.
    """

    def __init__(self, policy_file: str = "config/policy_config.yaml") -> None:
        """
        Initialize the YAML policy validator.

        Args:
            policy_file: Path to the YAML policy configuration file
        """
        self.policy_file = policy_file
        self.policies: Dict[int, Dict[str, Any]] = {}
        self._cidr_cache: Dict[str, ipaddress.IPv4Network] = {}

    def hash_string(self, s: str) -> int:
        """
        Calculate string hash value using djb2 algorithm.

        This method computes a hash value for strings, consistent with the
        BPF program implementation. Limited to first 16 characters for consistency.

        Args:
            s: String to hash

        Returns:
            32-bit hash value
        """
        return string_utils.hash_string(s)

    def ip_to_int(self, ip_str: str) -> int:
        """
        Convert IP address string to integer.

        Args:
            ip_str: IP address in dotted decimal notation

        Returns:
            IP address as 32-bit integer

        Raises:
            ValueError: If IP address format is invalid
        """
        return network_utils.ip_to_int(ip_str)

    def int_to_ip(self, ip_int: int) -> str:
        """
        Convert integer to IP address string.

        Args:
            ip_int: IP address as 32-bit integer

        Returns:
            IP address in dotted decimal notation

        Raises:
            ValueError: If integer is not a valid IP address
        """
        return network_utils.int_to_ip(ip_int)

    def _get_cidr_network(self, cidr_str: str) -> ipaddress.IPv4Network:
        """
        Get IPv4Network object for CIDR string with caching.

        Args:
            cidr_str: CIDR notation string (e.g., "***********/24")

        Returns:
            IPv4Network object

        Raises:
            ValueError: If CIDR format is invalid
        """
        if cidr_str in self._cidr_cache:
            return self._cidr_cache[cidr_str]

        try:
            # Handle special case for "allow all"
            if cidr_str == "0.0.0.0/0":
                network = ipaddress.IPv4Network(cidr_str, strict=False)
            else:
                network = ipaddress.IPv4Network(cidr_str, strict=False)

            self._cidr_cache[cidr_str] = network
            return network
        except (ipaddress.AddressValueError, ipaddress.NetmaskValueError) as e:
            # Fallback: try to parse as single IP
            try:
                ip_part = cidr_str.split('/')[0]
                network = ipaddress.IPv4Network(f"{ip_part}/32", strict=False)
                self._cidr_cache[cidr_str] = network
                return network
            except Exception:
                from bpf_network_policy.utils.bpf_utils import CIDRError
                raise CIDRError(f"Invalid CIDR format: {cidr_str}") from e

    def is_ip_in_cidr(self, ip_str: str, cidr_str: str) -> bool:
        """
        Check if IP address is within CIDR block.

        Args:
            ip_str: IP address to check
            cidr_str: CIDR block to check against

        Returns:
            True if IP is within CIDR block, False otherwise

        Raises:
            ValueError: If IP or CIDR format is invalid
        """
        try:
            ip_addr = ipaddress.IPv4Address(ip_str)
            network = self._get_cidr_network(cidr_str)
            return ip_addr in network
        except (ipaddress.AddressValueError, ValueError) as e:
            logger.warning(f"IP/CIDR validation error: {e}")
            return False
    
    def _validate_policy_config(self, config: Dict[str, Any]) -> None:
        """
        Validate the structure of policy configuration.

        Args:
            config: Loaded YAML configuration

        Raises:
            PolicyValidationError: If configuration structure is invalid
        """
        if not isinstance(config, dict):
            raise PolicyValidationError("Configuration must be a dictionary")

        if 'ports' not in config:
            raise PolicyValidationError("Configuration must contain 'ports' key")

        if not isinstance(config['ports'], list):
            raise PolicyValidationError("'ports' must be a list")

        for i, port_config in enumerate(config['ports']):
            if not isinstance(port_config, dict):
                raise PolicyValidationError(f"Port configuration {i} must be a dictionary")

            if 'ports' not in port_config:
                raise PolicyValidationError(f"Port configuration {i} must contain 'ports' key")

            # Validate CIDR formats if present
            cidr_config = port_config.get('cidr', {})
            for list_type in ['cidr_allow_list', 'cidr_deny_list']:
                cidr_list = cidr_config.get(list_type, [])
                for cidr in cidr_list:
                    try:
                        self._get_cidr_network(cidr)
                    except ValueError as e:
                        raise PolicyValidationError(f"Invalid CIDR in {list_type}: {e}") from e

    def load_policy(self) -> bool:
        """
        Load and parse YAML policy configuration.

        Returns:
            True if policy loaded successfully, False otherwise
        """
        print("=== YAML Policy Validation Program ===\n")

        try:
            # Check if policy file exists
            if not os.path.exists(self.policy_file):
                raise FileNotFoundError(f"Policy file not found: {self.policy_file}")

            # Load YAML configuration
            with open(self.policy_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)

            if config is None:
                raise PolicyValidationError("Policy file is empty or invalid")

            # Validate configuration structure
            self._validate_policy_config(config)

            print(f"✓ Successfully loaded YAML policy configuration from {self.policy_file}")

            # Parse and store policies
            self._parse_policies(config)

            print(f"✓ Parsed policies for {len(self.policies)} ports")
            logger.info(f"Loaded {len(self.policies)} port policies from {self.policy_file}")
            return True

        except (FileNotFoundError, yaml.YAMLError, PolicyValidationError) as e:
            error_msg = f"Failed to load policy: {e}"
            print(f"✗ {error_msg}")
            logger.error(error_msg)
            return False
        except Exception as e:
            error_msg = f"Unexpected error loading policy: {e}"
            print(f"✗ {error_msg}")
            logger.error(error_msg, exc_info=True)
            return False

    def _parse_policies(self, config: Dict[str, Any]) -> None:
        """
        Parse policy configuration and populate internal policy structure.

        Args:
            config: Validated YAML configuration
        """
        policy_id = 1

        for port_config in config['ports']:
            ports = port_config['ports']
            process_config = port_config.get('process', {})
            cidr_config = port_config.get('cidr', {})

            # Validate and convert port numbers
            for port_str in ports:
                try:
                    port_num = int(port_str)
                    if not (1 <= port_num <= 65535):
                        raise ValueError(f"Port number out of range: {port_num}")
                except ValueError as e:
                    logger.warning(f"Invalid port number '{port_str}': {e}")
                    continue

                self.policies[port_num] = {
                    'policy_id': policy_id,
                    'process_allow_list': process_config.get('process_allow_list', []),
                    'process_deny_list': process_config.get('process_deny_list', []),
                    'cidr_allow_list': cidr_config.get('cidr_allow_list', []),
                    'cidr_deny_list': cidr_config.get('cidr_deny_list', [])
                }

            policy_id += 1
    
    def check_connection(self, process_name: str, src_ip: str, dst_port: int) -> Tuple[bool, str]:
        """
        Check if a connection should be allowed based on policy rules.

        This method implements the core policy decision logic:
        1. Check if port has a policy
        2. Check process and IP deny lists (immediate rejection)
        3. Check process and IP allow lists (both must pass)

        Args:
            process_name: Name of the process attempting connection
            src_ip: Source IP address
            dst_port: Destination port number

        Returns:
            Tuple of (allowed: bool, reason: str)
        """
        # Input validation
        if not isinstance(process_name, str) or not process_name.strip():
            return False, "Invalid process name"

        if not isinstance(dst_port, int) or not (1 <= dst_port <= 65535):
            return False, "Invalid port number"

        try:
            # Validate IP address format
            ipaddress.IPv4Address(src_ip)
        except ipaddress.AddressValueError:
            return False, "Invalid IP address format"

        # Check if port has a policy
        if dst_port not in self.policies:
            logger.debug(f"Port {dst_port} not in policy configuration")
            return False, "Port not in policy configuration"

        policy = self.policies[dst_port]

        # Check process deny list (immediate rejection)
        if process_name in policy['process_deny_list']:
            logger.debug(f"Process {process_name} in deny list for port {dst_port}")
            return False, "Process in deny list"

        # Check IP deny list (immediate rejection)
        for cidr in policy['cidr_deny_list']:
            try:
                if self.is_ip_in_cidr(src_ip, cidr):
                    logger.debug(f"IP {src_ip} matches deny CIDR {cidr} for port {dst_port}")
                    return False, "IP in deny list"
            except ValueError as e:
                logger.warning(f"Error checking deny CIDR {cidr}: {e}")
                continue

        # Check process allow list
        process_allowed = process_name in policy['process_allow_list']

        # Check IP allow list
        ip_allowed = False
        for cidr in policy['cidr_allow_list']:
            try:
                if self.is_ip_in_cidr(src_ip, cidr):
                    ip_allowed = True
                    logger.debug(f"IP {src_ip} matches allow CIDR {cidr} for port {dst_port}")
                    break
            except ValueError as e:
                logger.warning(f"Error checking allow CIDR {cidr}: {e}")
                continue

        # Final decision logic
        if process_allowed and ip_allowed:
            return True, "Process and IP both in allow lists"
        elif not process_allowed and not ip_allowed:
            return False, "Process and IP not in allow lists"
        elif not process_allowed:
            return False, "Process not in allow list"
        elif not ip_allowed:
            return False, "IP not in allow list"
        else:
            return False, "Unknown reason"
    
    def show_policy_summary(self) -> None:
        """Display a comprehensive summary of loaded policies."""
        print("\n=== Policy Configuration Summary ===")

        if not self.policies:
            print("No policies loaded.")
            return

        total_ports = len(self.policies)
        total_processes = sum(
            len(policy['process_allow_list']) + len(policy['process_deny_list'])
            for policy in self.policies.values()
        )
        total_cidrs = sum(
            len(policy['cidr_allow_list']) + len(policy['cidr_deny_list'])
            for policy in self.policies.values()
        )

        print(f"Total ports configured: {total_ports}")
        print(f"Total process rules: {total_processes}")
        print(f"Total CIDR rules: {total_cidrs}")

        for port in sorted(self.policies.keys()):
            policy = self.policies[port]
            print(f"\nPort {port} (Policy ID: {policy['policy_id']}):")
            print(f"  Process allow list: {policy['process_allow_list']}")
            print(f"  Process deny list: {policy['process_deny_list']}")
            print(f"  CIDR allow list: {policy['cidr_allow_list']}")
            print(f"  CIDR deny list: {policy['cidr_deny_list']}")

    def run_test_cases(self) -> bool:
        """
        Run comprehensive test cases to validate policy logic.

        Returns:
            True if all test cases pass, False otherwise
        """
        print("\n=== Policy Test Cases ===")

        # Comprehensive test cases covering various scenarios
        test_cases = [
            # (process_name, src_ip, dst_port, expected_result, description)
            ("curl", "127.0.0.1", 80, True, "curl accessing local port 80"),
            ("wget", "127.0.0.1", 443, True, "wget accessing local port 443"),
            ("nc", "127.0.0.1", 80, False, "nc in deny list"),
            ("/usr/bin/malicious", "127.0.0.1", 80, False, "malicious program in deny list"),
            ("curl", "*******", 80, False, "curl accessing denied IP"),
            ("curl", "*******", 443, False, "curl accessing denied IP"),
            ("ssh", "*************", 22, True, "SSH access allows all IPs"),
            ("telnet", "127.0.0.1", 22, False, "telnet not in SSH allow list"),
            ("systemd-resolve", "*******", 53, True, "DNS resolution allows all IPs"),
            ("curl", "127.0.0.1", 8080, False, "port 8080 not in policy"),
            # Additional edge cases
            ("", "127.0.0.1", 80, False, "empty process name"),
            ("curl", "invalid-ip", 80, False, "invalid IP address"),
            ("curl", "127.0.0.1", 0, False, "invalid port number"),
            ("curl", "************", 80, True, "IP in allowed CIDR range"),
        ]

        passed = 0
        failed = 0

        for process, src_ip, port, expected, description in test_cases:
            try:
                allowed, reason = self.check_connection(process, src_ip, port)

                if allowed == expected:
                    status = "✓ PASS"
                    passed += 1
                else:
                    status = "✗ FAIL"
                    failed += 1

                result_str = "ALLOW" if allowed else "DENY"
                print(f"{status} | {description}")
                print(f"     {process} -> {src_ip}:{port} = {result_str} ({reason})")

            except Exception as e:
                status = "✗ ERROR"
                failed += 1
                print(f"{status} | {description}")
                print(f"     Exception: {e}")

        print(f"\n=== Test Results ===")
        print(f"Passed: {passed}, Failed: {failed}, Total: {len(test_cases)}")

        if failed == 0:
            print("🎉 All test cases passed! YAML policy configuration works correctly")
            logger.info("All policy test cases passed successfully")
        else:
            print("⚠️ Some test cases failed, please check policy configuration")
            logger.warning(f"{failed} out of {len(test_cases)} test cases failed")

        return failed == 0
    
    def interactive_test(self) -> None:
        """
        Run an interactive test session for policy validation.

        This method provides a user-friendly interface for testing
        policy rules with custom inputs.
        """
        print("\n=== Interactive Policy Testing ===")
        print("Enter 'quit' at any prompt to exit")

        while True:
            try:
                print("\nEnter test parameters:")

                # Get process name
                process = input("Process name: ").strip()
                if process.lower() == 'quit':
                    break

                # Get source IP
                src_ip = input("Source IP address: ").strip()
                if src_ip.lower() == 'quit':
                    break

                # Validate IP format
                try:
                    ipaddress.IPv4Address(src_ip)
                except ipaddress.AddressValueError:
                    print(f"Invalid IP address format: {src_ip}")
                    continue

                # Get destination port
                port = input("Destination port: ").strip()
                if port.lower() == 'quit':
                    break

                # Validate port number
                try:
                    port_num = int(port)
                    if not (1 <= port_num <= 65535):
                        print(f"Port number must be between 1 and 65535: {port}")
                        continue
                except ValueError:
                    print(f"Invalid port number: {port}")
                    continue

                # Check connection against policy
                allowed, reason = self.check_connection(process, src_ip, port_num)

                # Display result with color indicators
                result = "✓ ALLOWED" if allowed else "✗ DENIED"
                print(f"\nResult: {result}")
                print(f"Reason: {reason}")

                # Show applicable policy details if available
                if port_num in self.policies:
                    policy = self.policies[port_num]
                    print(f"\nApplicable policy (ID: {policy['policy_id']}):")
                    print(f"  Process allow list: {policy['process_allow_list']}")
                    print(f"  Process deny list: {policy['process_deny_list']}")
                    if len(policy['cidr_allow_list']) <= 5:  # Only show if not too long
                        print(f"  CIDR allow list: {policy['cidr_allow_list']}")
                    else:
                        print(f"  CIDR allow list: {len(policy['cidr_allow_list'])} entries")
                    if len(policy['cidr_deny_list']) <= 5:
                        print(f"  CIDR deny list: {policy['cidr_deny_list']}")
                    else:
                        print(f"  CIDR deny list: {len(policy['cidr_deny_list'])} entries")

            except KeyboardInterrupt:
                print("\nInterrupted by user")
                break
            except Exception as e:
                print(f"Error: {e}")
                logger.error(f"Interactive test error: {e}", exc_info=True)

        print("\nInteractive testing session ended")
    
    def run(self) -> bool:
        """
        Run the complete policy validation program.

        This method orchestrates the entire validation process:
        1. Load policy configuration
        2. Display policy summary
        3. Run automated test cases
        4. Optionally run interactive tests

        Returns:
            True if all automated tests pass, False otherwise
        """
        try:
            # Load policy configuration
            if not self.load_policy():
                return False

            # Display policy summary
            self.show_policy_summary()

            # Run automated test cases
            success = self.run_test_cases()

            # Optional interactive testing
            try:
                choice = input("\nRun interactive tests? (y/N): ").strip().lower()
                if choice in ('y', 'yes'):
                    self.interactive_test()
            except (KeyboardInterrupt, EOFError):
                print("\nSkipping interactive tests")

            return success

        except Exception as e:
            error_msg = f"Unexpected error in validation program: {e}"
            print(f"✗ {error_msg}")
            logger.error(error_msg, exc_info=True)
            return False


def main() -> None:
    """
    Main entry point for the YAML policy validation program.

    This function creates a validator instance, runs the validation,
    and exits with appropriate status code.
    """
    try:
        # Allow custom policy file via command line argument
        policy_file = sys.argv[1] if len(sys.argv) > 1 else "config/policy_config.yaml"

        validator = YAMLPolicyValidator(policy_file)
        success = validator.run()

        # Exit with appropriate status code
        exit_code = 0 if success else 1
        logger.info(f"Program completed with exit code: {exit_code}")
        sys.exit(exit_code)

    except KeyboardInterrupt:
        print("\nProgram interrupted by user")
        logger.info("Program interrupted by user")
        sys.exit(130)  # Standard exit code for SIGINT
    except Exception as e:
        error_msg = f"Fatal error: {e}"
        print(f"✗ {error_msg}")
        logger.error(error_msg, exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    main()
