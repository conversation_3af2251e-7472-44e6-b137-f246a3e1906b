#!/usr/bin/env python3
"""
核心逻辑测试程序

测试BPF网络策略的核心逻辑，不依赖BCC：
1. 哈希函数正确性
2. 策略配置逻辑
3. 路径处理功能
4. 工具类功能
"""

import os
import sys
import time

# Add the project root to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.bpf_network_policy.utils import (
    StringUtils, ProcessUtils, NetworkUtils, PolicyUtils,
    string_utils, process_utils, network_utils, policy_utils
)

class CoreLogicTester:
    """核心逻辑测试器"""
    
    def __init__(self):
        self.test_results = []
        
    def test_hash_functions(self):
        """测试哈希函数"""
        print("=== 测试1: 哈希函数 ===")
        
        try:
            # 测试短字符串哈希（进程名）
            test_names = ["curl", "wget", "ssh", "python3", "nginx"]
            
            print("进程名哈希测试:")
            print(f"{'进程名':<15} {'哈希值':<12} {'二进制(后8位)'}")
            print("-" * 45)
            
            for name in test_names:
                hash_val = string_utils.hash_string(name)
                binary = format(hash_val & 0xFF, '08b')  # 只显示后8位
                print(f"{name:<15} {hash_val:<12} {binary}")
            
            # 测试长字符串哈希（路径）
            test_paths = [
                "/usr/bin/curl",
                "/usr/bin/wget", 
                "/tmp/malicious_curl",
                "/opt/google/chrome/chrome",
                "/snap/firefox/current/usr/lib/firefox/firefox"
            ]
            
            print("\n路径哈希测试:")
            print(f"{'路径':<50} {'哈希值':<12}")
            print("-" * 65)
            
            for path in test_paths:
                hash_val = string_utils.hash_long_string(path)
                print(f"{path:<50} {hash_val:<12}")
            
            # 测试哈希一致性
            test_str = "/usr/bin/curl"
            hash1 = string_utils.hash_long_string(test_str)
            hash2 = string_utils.hash_long_string(test_str)
            
            if hash1 == hash2:
                print(f"\n✓ 哈希一致性验证通过: {hash1}")
                return True
            else:
                print(f"\n❌ 哈希不一致: {hash1} != {hash2}")
                return False
                
        except Exception as e:
            print(f"❌ 哈希函数测试失败: {e}")
            return False
    
    def test_process_utils(self):
        """测试进程工具类"""
        print("\n=== 测试2: 进程工具类 ===")
        
        try:
            # 测试当前进程路径获取
            current_pid = os.getpid()
            current_path = process_utils.get_process_path(current_pid)
            
            print(f"当前进程信息:")
            print(f"  PID: {current_pid}")
            print(f"  路径: {current_path}")
            
            if current_path:
                print("✓ 进程路径获取成功")
            else:
                print("⚠️  进程路径获取失败（可能是权限问题）")
            
            # 测试路径验证
            valid_paths = [
                "/usr/bin/curl",
                "/usr/bin/python3",
                "/opt/google/chrome/chrome"
            ]
            
            invalid_paths = [
                "",
                "   ",
                "relative/path",
                "a" * 5000  # 超长路径
            ]
            
            print("\n路径验证测试:")
            for path in valid_paths:
                if process_utils.validate_process_path(path):
                    print(f"✓ 有效路径: {path}")
                else:
                    print(f"❌ 路径验证失败: {path}")
                    return False
            
            for path in invalid_paths:
                if not process_utils.validate_process_path(path):
                    print(f"✓ 正确拒绝无效路径: {repr(path)}")
                else:
                    print(f"❌ 错误接受无效路径: {repr(path)}")
                    return False
            
            return True
            
        except Exception as e:
            print(f"❌ 进程工具类测试失败: {e}")
            return False
    
    def test_network_utils(self):
        """测试网络工具类"""
        print("\n=== 测试3: 网络工具类 ===")
        
        try:
            # 测试IP地址验证
            valid_ips = [
                "127.0.0.1",
                "***********",
                "********",
                "*******"
            ]
            
            invalid_ips = [
                "256.1.1.1",
                "192.168.1",
                "not.an.ip",
                ""
            ]
            
            print("IP地址验证测试:")
            for ip in valid_ips:
                if network_utils.validate_ip_address(ip):
                    print(f"✓ 有效IP: {ip}")
                else:
                    print(f"❌ IP验证失败: {ip}")
                    return False
            
            for ip in invalid_ips:
                if not network_utils.validate_ip_address(ip):
                    print(f"✓ 正确拒绝无效IP: {ip}")
                else:
                    print(f"❌ 错误接受无效IP: {ip}")
                    return False
            
            # 测试CIDR验证
            valid_cidrs = [
                "***********/24",
                "10.0.0.0/8",
                "127.0.0.1/32"
            ]
            
            invalid_cidrs = [
                "***********/33",
                "256.1.1.0/24",
                "***********"
            ]
            
            print("\nCIDR验证测试:")
            for cidr in valid_cidrs:
                if network_utils.validate_cidr(cidr):
                    print(f"✓ 有效CIDR: {cidr}")
                else:
                    print(f"❌ CIDR验证失败: {cidr}")
                    return False
            
            for cidr in invalid_cidrs:
                if not network_utils.validate_cidr(cidr):
                    print(f"✓ 正确拒绝无效CIDR: {cidr}")
                else:
                    print(f"❌ 错误接受无效CIDR: {cidr}")
                    return False
            
            return True
            
        except Exception as e:
            print(f"❌ 网络工具类测试失败: {e}")
            return False
    
    def test_policy_logic(self):
        """测试策略逻辑"""
        print("\n=== 测试4: 策略逻辑 ===")
        
        try:
            # 模拟策略配置
            policy_config = {
                "ports": [
                    {
                        "ports": ["80", "443"],
                        "process": {
                            "process_allow_list": ["curl", "wget"],
                            "process_path_allow_list": ["/usr/bin/curl", "/usr/bin/wget"],
                            "process_deny_list": ["malware"],
                            "process_path_deny_list": ["/tmp/malware"]
                        },
                        "cidr": {
                            "cidr_allow_list": ["127.0.0.1/32", "***********/24"],
                            "cidr_deny_list": ["*******/32"]
                        }
                    }
                ]
            }
            
            # 验证策略配置
            if policy_utils.validate_policy_config(policy_config):
                print("✓ 策略配置验证通过")
            else:
                print("❌ 策略配置验证失败")
                return False
            
            # 测试BPF键值计算
            policy_id = 1
            
            # 进程名键值
            proc_name = "curl"
            proc_hash = string_utils.hash_string(proc_name)
            bpf_key = (policy_id << 32) | proc_hash
            
            print(f"\nBPF键值计算测试:")
            print(f"进程名: {proc_name}")
            print(f"哈希值: {proc_hash}")
            print(f"策略ID: {policy_id}")
            print(f"BPF键: {bpf_key} (0x{bpf_key:016x})")
            
            # 进程路径键值
            proc_path = "/usr/bin/curl"
            path_hash = string_utils.hash_long_string(proc_path)
            path_bpf_key = (policy_id << 32) | path_hash
            
            print(f"\n进程路径: {proc_path}")
            print(f"路径哈希: {path_hash}")
            print(f"路径BPF键: {path_bpf_key} (0x{path_bpf_key:016x})")
            
            # 验证键值不同
            if bpf_key != path_bpf_key:
                print("✓ 进程名和路径产生不同的BPF键")
                return True
            else:
                print("❌ 进程名和路径产生相同的BPF键")
                return False
                
        except Exception as e:
            print(f"❌ 策略逻辑测试失败: {e}")
            return False
    
    def test_security_scenarios(self):
        """测试安全场景"""
        print("\n=== 测试5: 安全场景 ===")
        
        try:
            print("进程名欺骗攻击防护测试:")
            
            # 合法进程
            legitimate_name = "curl"
            legitimate_path = "/usr/bin/curl"
            
            # 恶意进程（相同名称，不同路径）
            malicious_name = "curl"  # 相同的进程名
            malicious_path = "/tmp/curl"  # 不同的路径
            
            # 计算哈希
            legit_name_hash = string_utils.hash_string(legitimate_name)
            legit_path_hash = string_utils.hash_long_string(legitimate_path)
            
            mal_name_hash = string_utils.hash_string(malicious_name)
            mal_path_hash = string_utils.hash_long_string(malicious_path)
            
            print(f"\n合法进程:")
            print(f"  名称: {legitimate_name} (哈希: {legit_name_hash})")
            print(f"  路径: {legitimate_path} (哈希: {legit_path_hash})")
            
            print(f"\n恶意进程:")
            print(f"  名称: {malicious_name} (哈希: {mal_name_hash})")
            print(f"  路径: {malicious_path} (哈希: {mal_path_hash})")
            
            # 验证防护效果
            if legit_name_hash == mal_name_hash:
                print("⚠️  进程名哈希相同 - 仅基于名称的策略会被绕过")
            else:
                print("❌ 进程名哈希不同 - 测试数据错误")
                return False
            
            if legit_path_hash != mal_path_hash:
                print("✓ 路径哈希不同 - 基于路径的策略可以防护")
                return True
            else:
                print("❌ 路径哈希相同 - 防护失效")
                return False
                
        except Exception as e:
            print(f"❌ 安全场景测试失败: {e}")
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        print("BPF网络策略核心逻辑测试")
        print("=" * 50)
        
        tests = [
            ("哈希函数", self.test_hash_functions),
            ("进程工具类", self.test_process_utils),
            ("网络工具类", self.test_network_utils),
            ("策略逻辑", self.test_policy_logic),
            ("安全场景", self.test_security_scenarios),
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            try:
                if test_func():
                    passed += 1
                    self.test_results.append((test_name, True, None))
                else:
                    self.test_results.append((test_name, False, "测试失败"))
            except Exception as e:
                print(f"❌ {test_name} 异常: {e}")
                self.test_results.append((test_name, False, str(e)))
        
        # 显示测试结果摘要
        print(f"\n{'='*50}")
        print("测试结果摘要:")
        print(f"通过: {passed}/{total}")
        
        for test_name, success, error in self.test_results:
            status = "✓ 通过" if success else f"✗ 失败 ({error})"
            print(f"  {test_name}: {status}")
        
        return passed == total

def main():
    """主函数"""
    tester = CoreLogicTester()
    success = tester.run_all_tests()
    
    if success:
        print("\n🎉 所有核心逻辑测试通过！")
        print("\n关键发现:")
        print("1. ✓ 哈希函数工作正常，能区分不同路径")
        print("2. ✓ 进程工具类可以获取和验证路径")
        print("3. ✓ 网络工具类正确验证IP和CIDR")
        print("4. ✓ 策略逻辑正确实现BPF键值计算")
        print("5. ✓ 路径控制可以防护进程名欺骗攻击")
        print("\n结论: 绝对路径匹配的核心逻辑是正确的！")
        return 0
    else:
        print("\n❌ 部分测试失败")
        return 1

if __name__ == "__main__":
    sys.exit(main())
