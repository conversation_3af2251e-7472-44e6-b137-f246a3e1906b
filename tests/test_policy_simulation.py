#!/usr/bin/env python3
"""
策略拦截模拟测试

模拟BPF程序的策略决策逻辑，验证拦截规则是否正确：
1. 模拟BPF Map结构
2. 实现策略决策逻辑
3. 测试各种连接场景
4. 验证拦截效果
"""

import os
import sys
import socket
from typing import Dict, List, Tuple, Optional

# Add the project root to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.bpf_network_policy.utils import StringUtils, string_utils

class PolicySimulator:
    """策略模拟器"""
    
    def __init__(self):
        # 模拟BPF Map
        self.port_policy_map: Dict[int, int] = {}
        self.process_allow_map: Dict[int, int] = {}
        self.process_deny_map: Dict[int, int] = {}
        self.ip_allow_map: Dict[int, int] = {}
        self.ip_deny_map: Dict[int, int] = {}
        self.policy_stats: Dict[int, int] = {0: 0, 1: 0, 2: 0}  # total, allowed, denied
        
    def ip_to_int(self, ip_str: str) -> int:
        """将IP字符串转换为整数"""
        parts = ip_str.split('.')
        return (int(parts[0]) << 24) | (int(parts[1]) << 16) | \
               (int(parts[2]) << 8) | int(parts[3])
    
    def configure_test_policies(self):
        """配置测试策略"""
        print("=== 配置测试策略 ===")
        
        # 1. 端口策略映射
        self.port_policy_map[80] = 1    # HTTP -> 策略1
        self.port_policy_map[443] = 1   # HTTPS -> 策略1  
        self.port_policy_map[22] = 2    # SSH -> 策略2
        self.port_policy_map[8080] = 3  # 测试端口 -> 策略3
        print("✓ 配置端口策略映射")
        
        # 2. 进程白名单（策略1: HTTP/HTTPS）
        policy_id = 1
        allowed_processes = ["curl", "wget", "python3", "firefox"]
        
        for proc_name in allowed_processes:
            proc_hash = string_utils.hash_string(proc_name)
            bpf_key = (policy_id << 32) | proc_hash
            self.process_allow_map[bpf_key] = 1
            print(f"✓ 策略{policy_id} 进程白名单: {proc_name} (哈希: {proc_hash})")
        
        # 3. 进程白名单（策略2: SSH）
        policy_id = 2
        ssh_processes = ["ssh", "scp", "sftp"]
        
        for proc_name in ssh_processes:
            proc_hash = string_utils.hash_string(proc_name)
            bpf_key = (policy_id << 32) | proc_hash
            self.process_allow_map[bpf_key] = 1
            print(f"✓ 策略{policy_id} 进程白名单: {proc_name}")
        
        # 4. 进程黑名单（策略3: 测试端口）
        policy_id = 3
        denied_processes = ["python3", "nc", "telnet"]
        
        for proc_name in denied_processes:
            proc_hash = string_utils.hash_string(proc_name)
            bpf_key = (policy_id << 32) | proc_hash
            self.process_deny_map[bpf_key] = 1
            print(f"✓ 策略{policy_id} 进程黑名单: {proc_name}")
        
        # 5. IP白名单
        allowed_ips = [
            "127.0.0.1",      # 本地
            "***********",    # 局域网
            "*******",        # Google DNS
            "*******",        # Cloudflare DNS
        ]
        
        for policy_id in [1, 2]:  # 策略1和2允许这些IP
            for ip_str in allowed_ips:
                ip_int = self.ip_to_int(ip_str)
                ip_key = (policy_id << 32) | ip_int
                self.ip_allow_map[ip_key] = 1
                print(f"✓ 策略{policy_id} IP白名单: {ip_str}")
        
        # 6. IP黑名单（策略3）
        policy_id = 3
        denied_ips = ["0.0.0.0", "***************"]
        
        for ip_str in denied_ips:
            ip_int = self.ip_to_int(ip_str)
            ip_key = (policy_id << 32) | ip_int
            self.ip_deny_map[ip_key] = 1
            print(f"✓ 策略{policy_id} IP黑名单: {ip_str}")
    
    def check_connection_policy(self, process_name: str, dst_ip: str, dst_port: int) -> Tuple[bool, str]:
        """模拟BPF程序的策略检查逻辑"""
        
        # 更新总连接数
        self.policy_stats[0] += 1
        
        # 1. 查找端口对应的策略ID
        policy_id = self.port_policy_map.get(dst_port)
        if policy_id is None:
            # 端口不在任何策略中，默认允许
            self.policy_stats[1] += 1
            return True, f"端口{dst_port}不在策略中，默认允许"
        
        # 2. 计算哈希
        proc_hash = string_utils.hash_string(process_name)
        ip_int = self.ip_to_int(dst_ip)
        
        # 3. 检查进程黑名单
        process_deny_key = (policy_id << 32) | proc_hash
        if process_deny_key in self.process_deny_map:
            self.policy_stats[2] += 1
            return False, f"进程{process_name}在策略{policy_id}的黑名单中"
        
        # 4. 检查IP黑名单
        ip_deny_key = (policy_id << 32) | ip_int
        if ip_deny_key in self.ip_deny_map:
            self.policy_stats[2] += 1
            return False, f"IP{dst_ip}在策略{policy_id}的黑名单中"
        
        # 5. 检查进程白名单
        process_allow_key = (policy_id << 32) | proc_hash
        process_in_whitelist = process_allow_key in self.process_allow_map
        
        # 6. 检查IP白名单
        ip_allow_key = (policy_id << 32) | ip_int
        ip_in_whitelist = ip_allow_key in self.ip_allow_map
        
        # 7. 决策逻辑：进程在白名单 AND IP在白名单
        if process_in_whitelist and ip_in_whitelist:
            self.policy_stats[1] += 1
            return True, f"进程{process_name}和IP{dst_ip}都在策略{policy_id}白名单中"
        elif not process_in_whitelist:
            self.policy_stats[2] += 1
            return False, f"进程{process_name}不在策略{policy_id}白名单中"
        else:  # not ip_in_whitelist
            self.policy_stats[2] += 1
            return False, f"IP{dst_ip}不在策略{policy_id}白名单中"
    
    def test_connection_scenarios(self):
        """测试各种连接场景"""
        print("\n=== 测试连接场景 ===")
        
        # 测试用例：(进程名, 目标IP, 目标端口, 预期结果, 描述)
        test_cases = [
            # HTTP/HTTPS 测试（策略1）
            ("curl", "*******", 80, True, "curl访问Google DNS HTTP - 应该允许"),
            ("wget", "*******", 80, True, "wget访问Cloudflare HTTP - 应该允许"),
            ("python3", "127.0.0.1", 443, True, "python3访问本地HTTPS - 应该允许"),
            ("firefox", "***********", 80, True, "firefox访问局域网HTTP - 应该允许"),
            ("malware", "*******", 80, False, "恶意程序访问HTTP - 应该拒绝"),
            ("curl", "********", 80, False, "curl访问未授权IP - 应该拒绝"),
            
            # SSH 测试（策略2）
            ("ssh", "***********0", 22, True, "ssh访问局域网 - 应该允许"),
            ("scp", "127.0.0.1", 22, True, "scp访问本地 - 应该允许"),
            ("python3", "***********0", 22, False, "python3访问SSH - 应该拒绝"),
            ("ssh", "********", 22, False, "ssh访问未授权IP - 应该拒绝"),
            
            # 测试端口（策略3）
            ("curl", "127.0.0.1", 8080, True, "curl访问8080 - 应该允许（不在黑名单）"),
            ("python3", "127.0.0.1", 8080, False, "python3访问8080 - 应该拒绝（在黑名单）"),
            ("nc", "***********", 8080, False, "nc访问8080 - 应该拒绝（在黑名单）"),
            
            # 未配置端口
            ("curl", "*******", 9999, True, "curl访问未配置端口 - 默认允许"),
            ("malware", "*******", 9999, True, "恶意程序访问未配置端口 - 默认允许"),
        ]
        
        print(f"{'序号':<4} {'进程':<10} {'目标IP':<15} {'端口':<6} {'结果':<6} {'预期':<6} {'匹配':<6} {'原因'}")
        print("-" * 90)
        
        passed = 0
        total = len(test_cases)
        
        for i, (process, ip, port, expected, description) in enumerate(test_cases, 1):
            allowed, reason = self.check_connection_policy(process, ip, port)
            
            result_str = "允许" if allowed else "拒绝"
            expected_str = "允许" if expected else "拒绝"
            match_str = "✓" if (allowed == expected) else "✗"
            
            if allowed == expected:
                passed += 1
            
            print(f"{i:<4} {process:<10} {ip:<15} {port:<6} {result_str:<6} {expected_str:<6} {match_str:<6} {reason}")
        
        print(f"\n策略测试结果: {passed}/{total} 通过")
        return passed == total
    
    def test_security_scenarios(self):
        """测试安全场景"""
        print("\n=== 测试安全场景 ===")
        
        # 进程名欺骗攻击测试
        print("1. 进程名欺骗攻击防护:")
        
        # 合法curl vs 恶意curl（相同进程名，不同路径）
        legitimate_curl = "curl"
        malicious_curl = "curl"  # 进程名相同
        
        # 测试相同的连接
        target_ip = "*******"
        target_port = 80
        
        legit_allowed, legit_reason = self.check_connection_policy(legitimate_curl, target_ip, target_port)
        mal_allowed, mal_reason = self.check_connection_policy(malicious_curl, target_ip, target_port)
        
        print(f"  合法curl: {'允许' if legit_allowed else '拒绝'} - {legit_reason}")
        print(f"  恶意curl: {'允许' if mal_allowed else '拒绝'} - {mal_reason}")
        
        if legit_allowed == mal_allowed:
            print("  ⚠️  仅基于进程名的策略无法区分合法和恶意程序")
            print("  💡 建议：使用进程绝对路径进行更精确的控制")
        
        # 端口扫描攻击测试
        print("\n2. 端口扫描攻击防护:")
        
        scanner_process = "nmap"
        scan_targets = [
            ("127.0.0.1", 22),
            ("127.0.0.1", 80),
            ("127.0.0.1", 443),
            ("127.0.0.1", 8080),
        ]
        
        blocked_count = 0
        for ip, port in scan_targets:
            allowed, reason = self.check_connection_policy(scanner_process, ip, port)
            status = "允许" if allowed else "拒绝"
            print(f"  扫描 {ip}:{port} - {status}")
            if not allowed:
                blocked_count += 1
        
        print(f"  拦截率: {blocked_count}/{len(scan_targets)} ({blocked_count/len(scan_targets)*100:.1f}%)")
        
        return True
    
    def show_statistics(self):
        """显示统计信息"""
        print("\n=== 统计信息 ===")
        
        total = self.policy_stats[0]
        allowed = self.policy_stats[1]
        denied = self.policy_stats[2]
        
        print(f"总连接尝试: {total}")
        print(f"允许连接: {allowed} ({allowed/total*100:.1f}%)")
        print(f"拒绝连接: {denied} ({denied/total*100:.1f}%)")
        
        # 显示策略配置摘要
        print(f"\n策略配置摘要:")
        print(f"端口策略: {len(self.port_policy_map)} 个端口")
        print(f"进程白名单: {len(self.process_allow_map)} 条规则")
        print(f"进程黑名单: {len(self.process_deny_map)} 条规则")
        print(f"IP白名单: {len(self.ip_allow_map)} 条规则")
        print(f"IP黑名单: {len(self.ip_deny_map)} 条规则")
    
    def run_simulation(self):
        """运行完整模拟测试"""
        print("BPF网络策略拦截模拟测试")
        print("=" * 50)
        
        try:
            # 1. 配置策略
            self.configure_test_policies()
            
            # 2. 测试连接场景
            scenarios_passed = self.test_connection_scenarios()
            
            # 3. 测试安全场景
            self.test_security_scenarios()
            
            # 4. 显示统计
            self.show_statistics()
            
            if scenarios_passed:
                print("\n🎉 策略拦截模拟测试通过！")
                print("\n关键发现:")
                print("1. ✓ 策略决策逻辑正确实现")
                print("2. ✓ 进程和IP双重白名单机制有效")
                print("3. ✓ 黑名单优先级高于白名单")
                print("4. ✓ 未配置端口默认允许")
                print("5. ⚠️  仅基于进程名无法防护进程名欺骗")
                print("6. 💡 建议使用进程绝对路径提高安全性")
                return True
            else:
                print("\n❌ 部分策略测试失败")
                return False
                
        except Exception as e:
            print(f"\n❌ 模拟测试过程中出现异常: {e}")
            return False

def main():
    """主函数"""
    simulator = PolicySimulator()
    success = simulator.run_simulation()
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
