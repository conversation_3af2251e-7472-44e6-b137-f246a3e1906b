#!/usr/bin/env python3
"""
测试BPF路径策略控制程序

这个脚本测试 bpf_path_policy_control.c 的功能，验证：
1. BPF程序能否正确编译和加载
2. 路径策略配置是否正确
3. 路径匹配逻辑是否工作
"""

import os
import sys
import time
import subprocess
from typing import Dict, List, Optional

# Add the project root to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from bcc import BPF
from src.bpf_network_policy.utils import (
    StringUtils, SystemUtils, string_utils, system_utils
)

class PathPolicyTester:
    """BPF路径策略测试器"""
    
    def __init__(self):
        self.bpf = None
        self.test_results = []
        
    def test_bpf_compilation(self):
        """测试BPF程序编译"""
        print("=== 测试1: BPF程序编译 ===")
        
        try:
            # 读取BPF程序源码
            bpf_file = "src/bpf_network_policy/bpf/bpf_simple_path_control.c"
            if not os.path.exists(bpf_file):
                print(f"❌ BPF程序文件不存在: {bpf_file}")
                return False
                
            with open(bpf_file, 'r') as f:
                bpf_code = f.read()
            
            print(f"✓ 读取BPF程序: {len(bpf_code)} 字符")
            
            # 尝试编译BPF程序
            self.bpf = BPF(text=bpf_code)
            print("✓ BPF程序编译成功")
            
            # 检查BPF Map是否创建成功
            maps_to_check = [
                "port_policy_map",
                "process_allow_map", 
                "process_deny_map",
                "process_path_allow_map",
                "process_path_deny_map",
                "cidr_allow_map",
                "cidr_deny_map",
                "policy_stats"
            ]
            
            for map_name in maps_to_check:
                if hasattr(self.bpf, map_name):
                    print(f"✓ BPF Map创建成功: {map_name}")
                else:
                    print(f"❌ BPF Map创建失败: {map_name}")
                    return False
            
            return True
            
        except Exception as e:
            print(f"❌ BPF程序编译失败: {e}")
            return False
    
    def test_policy_configuration(self):
        """测试策略配置"""
        print("\n=== 测试2: 策略配置 ===")
        
        if not self.bpf:
            print("❌ BPF程序未加载")
            return False
        
        try:
            # 配置端口策略映射
            port_policy_map = self.bpf.get_table("port_policy_map")
            port_policy_map[80] = 1   # 端口80对应策略ID 1
            port_policy_map[443] = 1  # 端口443对应策略ID 1
            port_policy_map[22] = 2   # 端口22对应策略ID 2
            print("✓ 端口策略映射配置完成")
            
            # 配置进程路径白名单
            process_path_allow_map = self.bpf.get_table("process_path_allow_map")
            
            # 策略ID 1的路径白名单
            policy_id = 1
            allowed_paths = [
                "/usr/bin/curl",
                "/usr/bin/wget", 
                "/usr/bin/firefox"
            ]
            
            for path in allowed_paths:
                path_hash = string_utils.hash_long_string(path)
                bpf_key = (policy_id << 32) | path_hash
                process_path_allow_map[bpf_key] = 1
                print(f"✓ 添加路径白名单: {path} (哈希: {path_hash})")
            
            # 配置进程路径黑名单
            process_path_deny_map = self.bpf.get_table("process_path_deny_map")
            
            denied_paths = [
                "/tmp/curl",
                "/tmp/malware",
                "/var/tmp/suspicious"
            ]
            
            for path in denied_paths:
                path_hash = string_utils.hash_long_string(path)
                bpf_key = (policy_id << 32) | path_hash
                process_path_deny_map[bpf_key] = 1
                print(f"✓ 添加路径黑名单: {path} (哈希: {path_hash})")
            
            # 配置IP白名单（简单测试）
            cidr_allow_map = self.bpf.get_table("cidr_allow_map")
            # 允许本地连接
            localhost_ip = 0x7f000001  # 127.0.0.1
            ip_key = (policy_id << 32) | localhost_ip
            cidr_allow_map[ip_key] = 1
            print("✓ 添加IP白名单: 127.0.0.1")
            
            return True
            
        except Exception as e:
            print(f"❌ 策略配置失败: {e}")
            return False
    
    def test_hash_calculation(self):
        """测试哈希计算"""
        print("\n=== 测试3: 哈希计算 ===")
        
        test_paths = [
            "/usr/bin/curl",
            "/usr/bin/wget",
            "/tmp/curl",
            "/usr/bin/python3",
            "/opt/google/chrome/chrome"
        ]
        
        print("路径哈希计算结果:")
        print(f"{'路径':<40} {'哈希值':<12}")
        print("-" * 55)
        
        for path in test_paths:
            hash_val = string_utils.hash_long_string(path)
            print(f"{path:<40} {hash_val:<12}")
        
        # 验证哈希一致性
        path = "/usr/bin/curl"
        hash1 = string_utils.hash_long_string(path)
        hash2 = string_utils.hash_long_string(path)
        
        if hash1 == hash2:
            print("✓ 哈希计算一致性验证通过")
            return True
        else:
            print(f"❌ 哈希计算不一致: {hash1} != {hash2}")
            return False
    
    def test_bpf_function_attachment(self):
        """测试BPF函数附加"""
        print("\n=== 测试4: BPF函数附加 ===")
        
        if not self.bpf:
            print("❌ BPF程序未加载")
            return False
        
        try:
            # 检查BPF函数是否存在
            function_name = "check_connection_policy"
            if hasattr(self.bpf, function_name):
                print(f"✓ BPF函数存在: {function_name}")
            else:
                print(f"❌ BPF函数不存在: {function_name}")
                return False
            
            # 注意：实际的函数附加需要特定的内核挂载点
            # 这里只是验证函数是否可以被找到
            print("✓ BPF函数验证通过")
            print("ℹ️  实际附加需要root权限和适当的内核挂载点")
            
            return True
            
        except Exception as e:
            print(f"❌ BPF函数附加测试失败: {e}")
            return False
    
    def test_map_operations(self):
        """测试BPF Map操作"""
        print("\n=== 测试5: BPF Map操作 ===")
        
        if not self.bpf:
            print("❌ BPF程序未加载")
            return False
        
        try:
            # 测试统计Map
            policy_stats = self.bpf.get_table("policy_stats")
            
            # 写入测试数据
            policy_stats[0] = 100  # 总连接数
            policy_stats[1] = 80   # 允许连接数
            policy_stats[2] = 20   # 拒绝连接数
            
            # 读取验证
            total = policy_stats[0].value
            allowed = policy_stats[1].value
            denied = policy_stats[2].value
            
            print(f"✓ 统计数据写入/读取成功:")
            print(f"  总连接数: {total}")
            print(f"  允许连接: {allowed}")
            print(f"  拒绝连接: {denied}")
            
            # 测试路径Map查找
            process_path_allow_map = self.bpf.get_table("process_path_allow_map")
            
            # 构造测试键
            policy_id = 1
            path_hash = string_utils.hash_long_string("/usr/bin/curl")
            test_key = (policy_id << 32) | path_hash
            
            # 检查是否存在
            try:
                value = process_path_allow_map[test_key].value
                print(f"✓ 路径白名单查找成功: key={test_key}, value={value}")
            except KeyError:
                print(f"ℹ️  路径白名单中未找到测试键: {test_key}")
            
            return True
            
        except Exception as e:
            print(f"❌ BPF Map操作失败: {e}")
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        print("BPF路径策略控制程序测试")
        print("=" * 50)
        
        # 检查权限
        if not system_utils.check_root_privileges():
            print("⚠️  警告: 未以root权限运行，某些测试可能失败")
        
        tests = [
            ("BPF程序编译", self.test_bpf_compilation),
            ("策略配置", self.test_policy_configuration),
            ("哈希计算", self.test_hash_calculation),
            ("BPF函数附加", self.test_bpf_function_attachment),
            ("BPF Map操作", self.test_map_operations),
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            try:
                if test_func():
                    passed += 1
                    self.test_results.append((test_name, True, None))
                else:
                    self.test_results.append((test_name, False, "测试失败"))
            except Exception as e:
                print(f"❌ {test_name} 异常: {e}")
                self.test_results.append((test_name, False, str(e)))
        
        # 显示测试结果摘要
        print(f"\n{'='*50}")
        print("测试结果摘要:")
        print(f"通过: {passed}/{total}")
        
        for test_name, success, error in self.test_results:
            status = "✓ 通过" if success else f"✗ 失败 ({error})"
            print(f"  {test_name}: {status}")
        
        if self.bpf:
            print("\n清理资源...")
            # BCC会自动清理资源
        
        return passed == total

def main():
    """主函数"""
    tester = PathPolicyTester()
    success = tester.run_all_tests()
    
    if success:
        print("\n🎉 所有测试通过！")
        return 0
    else:
        print("\n❌ 部分测试失败")
        return 1

if __name__ == "__main__":
    sys.exit(main())
