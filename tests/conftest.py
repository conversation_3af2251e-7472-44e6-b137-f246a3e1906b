"""
Pytest Configuration for BPF Network Policy Control System Tests

This module provides common test fixtures and configuration for all tests
in the BPF network policy control system.
"""

import pytest
import sys
import os
from pathlib import Path

# Add the src directory to Python path for imports
src_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_path))

# Test configuration
pytest_plugins = []

@pytest.fixture(scope="session")
def test_config_dir():
    """Provide path to test configuration directory."""
    return Path(__file__).parent.parent / "config"

@pytest.fixture(scope="session") 
def test_policy_config(test_config_dir):
    """Provide path to test policy configuration file."""
    return test_config_dir / "policy_config.yaml"

@pytest.fixture(scope="session")
def example_policy_config(test_config_dir):
    """Provide path to example policy configuration file."""
    return test_config_dir / "examples" / "example_policy.yaml"

@pytest.fixture(scope="session")
def bpf_programs_dir():
    """Provide path to BPF programs directory."""
    return Path(__file__).parent.parent / "src" / "bpf_network_policy" / "bpf"

@pytest.fixture(scope="function")
def mock_root_privileges(monkeypatch):
    """Mock root privileges for testing."""
    monkeypatch.setattr(os, "geteuid", lambda: 0)

@pytest.fixture(scope="function")
def mock_non_root_privileges(monkeypatch):
    """Mock non-root privileges for testing."""
    monkeypatch.setattr(os, "geteuid", lambda: 1000)

# Test markers
def pytest_configure(config):
    """Configure custom pytest markers."""
    config.addinivalue_line(
        "markers", "unit: mark test as a unit test"
    )
    config.addinivalue_line(
        "markers", "integration: mark test as an integration test"
    )
    config.addinivalue_line(
        "markers", "requires_root: mark test as requiring root privileges"
    )
    config.addinivalue_line(
        "markers", "requires_bcc: mark test as requiring BCC framework"
    )
    config.addinivalue_line(
        "markers", "slow: mark test as slow running"
    )
