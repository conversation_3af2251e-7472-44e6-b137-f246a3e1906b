#!/usr/bin/env python3
"""
真实eBPF程序测试

尝试加载和运行真正的eBPF程序，验证网络拦截功能
"""

import os
import sys
import time
import socket
import threading

def test_basic_ebpf():
    """测试基本的eBPF功能"""
    print("=== 测试基本eBPF功能 ===")
    
    # 检查权限
    if os.getuid() != 0:
        print("❌ 需要root权限运行eBPF程序")
        print("请使用: sudo python3 tests/test_real_ebpf.py")
        return False
    
    try:
        from bcc import BPF
        print("✓ BCC导入成功")
    except ImportError as e:
        print(f"❌ BCC导入失败: {e}")
        print("请安装BCC: sudo apt-get install bpfcc-tools python3-bpfcc")
        return False
    
    # 简单的eBPF程序 - 只统计连接
    bpf_code = """
#include <uapi/linux/ptrace.h>
#include <linux/socket.h>

// 统计Map
BPF_HASH(connection_count, u32, u64, 10);

int trace_connect(struct bpf_sock_addr *ctx) {
    u32 key = 0;
    u64 *count = connection_count.lookup(&key);
    if (count) {
        (*count)++;
    } else {
        u64 init_val = 1;
        connection_count.update(&key, &init_val);
    }
    
    // 允许所有连接（只统计，不拦截）
    return 1;
}
"""
    
    try:
        # 编译eBPF程序
        bpf = BPF(text=bpf_code)
        print("✓ eBPF程序编译成功")
        
        # 获取函数
        fn = bpf.load_func("trace_connect", BPF.CGROUP_SOCK_ADDR)
        print("✓ eBPF函数加载成功")
        
        # 创建测试cgroup
        cgroup_path = "/sys/fs/cgroup/test_ebpf"
        if not os.path.exists(cgroup_path):
            os.makedirs(cgroup_path)
        
        # 将当前进程加入cgroup
        with open(f"{cgroup_path}/cgroup.procs", "w") as f:
            f.write(str(os.getpid()))
        print(f"✓ 进程加入cgroup: {cgroup_path}")
        
        # 附加eBPF程序到cgroup
        bpf.attach_func(fn, cgroup_path, BPF.CGROUP_INET4_CONNECT)
        print("✓ eBPF程序附加到cgroup成功")
        
        # 测试网络连接
        print("\n=== 测试网络连接 ===")
        test_connections = [
            ("127.0.0.1", 80),
            ("*******", 53),
            ("*******", 53),
        ]
        
        for host, port in test_connections:
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(2)
                result = sock.connect_ex((host, port))
                sock.close()
                
                status = "成功" if result == 0 else "失败"
                print(f"连接 {host}:{port} - {status}")
            except Exception as e:
                print(f"连接 {host}:{port} - 异常: {e}")
        
        # 检查统计信息
        time.sleep(1)  # 等待统计更新
        connection_count = bpf.get_table("connection_count")
        count = connection_count[0].value if 0 in connection_count else 0
        print(f"\n✓ eBPF统计到 {count} 次连接尝试")
        
        # 清理
        bpf.detach_func(fn, cgroup_path, BPF.CGROUP_INET4_CONNECT)
        print("✓ eBPF程序已分离")
        
        return True
        
    except Exception as e:
        print(f"❌ eBPF测试失败: {e}")
        return False

def test_network_policy_ebpf():
    """测试网络策略eBPF程序"""
    print("\n=== 测试网络策略eBPF程序 ===")
    
    if os.getuid() != 0:
        print("❌ 需要root权限")
        return False
    
    try:
        from bcc import BPF
        
        # 网络策略eBPF程序
        bpf_code = """
#include <uapi/linux/ptrace.h>
#include <linux/socket.h>

// 简单的策略Map
BPF_HASH(allowed_ports, u16, u8, 100);
BPF_HASH(connection_stats, u32, u64, 10);

int network_policy_filter(struct bpf_sock_addr *ctx) {
    u16 port = bpf_ntohs(ctx->user_port);
    
    // 更新总连接数
    u32 total_key = 0;
    u64 *total = connection_stats.lookup(&total_key);
    if (total) {
        (*total)++;
    } else {
        u64 init_val = 1;
        connection_stats.update(&total_key, &init_val);
    }
    
    // 检查端口是否被允许
    u8 *allowed = allowed_ports.lookup(&port);
    if (allowed && *allowed == 1) {
        // 更新允许连接数
        u32 allowed_key = 1;
        u64 *allowed_count = connection_stats.lookup(&allowed_key);
        if (allowed_count) {
            (*allowed_count)++;
        } else {
            u64 init_val = 1;
            connection_stats.update(&allowed_key, &init_val);
        }
        return 1; // 允许
    } else {
        // 更新拒绝连接数
        u32 denied_key = 2;
        u64 *denied_count = connection_stats.lookup(&denied_key);
        if (denied_count) {
            (*denied_count)++;
        } else {
            u64 init_val = 1;
            connection_stats.update(&denied_key, &init_val);
        }
        return 0; // 拒绝
    }
}
"""
        
        # 编译程序
        bpf = BPF(text=bpf_code)
        print("✓ 网络策略eBPF程序编译成功")
        
        # 配置允许的端口
        allowed_ports = bpf.get_table("allowed_ports")
        allowed_ports[53] = 1   # DNS
        allowed_ports[80] = 1   # HTTP
        allowed_ports[443] = 1  # HTTPS
        print("✓ 配置允许端口: 53, 80, 443")
        
        # 加载和附加函数
        fn = bpf.load_func("network_policy_filter", BPF.CGROUP_SOCK_ADDR)
        
        cgroup_path = "/sys/fs/cgroup/test_policy"
        if not os.path.exists(cgroup_path):
            os.makedirs(cgroup_path)
        
        with open(f"{cgroup_path}/cgroup.procs", "w") as f:
            f.write(str(os.getpid()))
        
        bpf.attach_func(fn, cgroup_path, BPF.CGROUP_INET4_CONNECT)
        print("✓ 网络策略eBPF程序附加成功")
        
        # 测试连接
        print("\n=== 测试策略拦截 ===")
        test_cases = [
            ("*******", 53, True, "DNS查询 - 应该允许"),
            ("127.0.0.1", 80, True, "HTTP连接 - 应该允许"),
            ("127.0.0.1", 22, False, "SSH连接 - 应该拒绝"),
            ("127.0.0.1", 8080, False, "8080端口 - 应该拒绝"),
        ]
        
        for host, port, should_succeed, description in test_cases:
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(1)
                result = sock.connect_ex((host, port))
                sock.close()
                
                success = (result == 0)
                expected = "成功" if should_succeed else "失败"
                actual = "成功" if success else "失败"
                match = "✓" if (success == should_succeed) else "✗"
                
                print(f"{match} {description}")
                print(f"   连接 {host}:{port} - {actual} (预期: {expected})")
                
            except Exception as e:
                print(f"✗ {description} - 异常: {e}")
        
        # 显示统计信息
        time.sleep(1)
        connection_stats = bpf.get_table("connection_stats")
        
        total = connection_stats[0].value if 0 in connection_stats else 0
        allowed = connection_stats[1].value if 1 in connection_stats else 0
        denied = connection_stats[2].value if 2 in connection_stats else 0
        
        print(f"\n=== eBPF统计信息 ===")
        print(f"总连接尝试: {total}")
        print(f"允许连接: {allowed}")
        print(f"拒绝连接: {denied}")
        
        # 清理
        bpf.detach_func(fn, cgroup_path, BPF.CGROUP_INET4_CONNECT)
        print("✓ 网络策略eBPF程序已分离")
        
        return True
        
    except Exception as e:
        print(f"❌ 网络策略eBPF测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("真实eBPF网络拦截测试")
    print("=" * 40)
    
    # 检查基本要求
    if os.getuid() != 0:
        print("❌ 此测试需要root权限")
        print("请使用: sudo python3 tests/test_real_ebpf.py")
        return 1
    
    success = True
    
    # 测试基本eBPF功能
    if not test_basic_ebpf():
        success = False
    
    # 测试网络策略eBPF
    if not test_network_policy_ebpf():
        success = False
    
    if success:
        print("\n🎉 真实eBPF网络拦截测试成功！")
        print("eBPF程序能够真正拦截网络连接")
    else:
        print("\n❌ eBPF测试失败")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
