#!/usr/bin/env python3
"""
Unit tests for the optimized YAML policy validator.

This module provides comprehensive unit tests to verify the functionality
of the optimized YAMLPolicyValidator class.
"""
import unittest
import tempfile
import os
import yaml
from unittest.mock import patch, mock_open
# Import the validator from integration tests
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent / "integration"))
from test_yaml_simple import YAMLPolicyValidator, PolicyValidationError

# Import exception types
from bpf_network_policy.utils.bpf_utils import IPAddressError, CIDRError


class TestYAMLPolicyValidator(unittest.TestCase):
    """Test cases for YAMLPolicyValidator class."""
    
    def setUp(self):
        """Set up test fixtures before each test method."""
        self.validator = YAMLPolicyValidator()
        
        # Sample policy configuration for testing
        self.sample_config = {
            'ports': [
                {
                    'ports': ['80', '443'],
                    'process': {
                        'process_allow_list': ['curl', 'wget'],
                        'process_deny_list': ['nc', 'malicious']
                    },
                    'cidr': {
                        'cidr_allow_list': ['127.0.0.1/32', '***********/24'],
                        'cidr_deny_list': ['*******/32']
                    }
                },
                {
                    'ports': ['22'],
                    'process': {
                        'process_allow_list': ['ssh'],
                        'process_deny_list': []
                    },
                    'cidr': {
                        'cidr_allow_list': ['0.0.0.0/0'],
                        'cidr_deny_list': []
                    }
                }
            ]
        }
    
    def test_hash_string(self):
        """Test string hashing functionality."""
        # Test normal string
        hash1 = self.validator.hash_string("test")
        hash2 = self.validator.hash_string("test")
        self.assertEqual(hash1, hash2, "Same string should produce same hash")
        
        # Test different strings
        hash3 = self.validator.hash_string("different")
        self.assertNotEqual(hash1, hash3, "Different strings should produce different hashes")
        
        # Test empty string
        hash_empty = self.validator.hash_string("")
        self.assertIsInstance(hash_empty, int)
        
        # Test long string (should be truncated to 16 chars)
        long_string = "a" * 20
        hash_long = self.validator.hash_string(long_string)
        hash_truncated = self.validator.hash_string("a" * 16)
        self.assertEqual(hash_long, hash_truncated)
        
        # Test invalid input
        with self.assertRaises(TypeError):
            self.validator.hash_string(123)
    
    def test_ip_conversion(self):
        """Test IP address conversion methods."""
        # Test valid IP addresses
        test_ips = ["127.0.0.1", "***********", "*******", "0.0.0.0", "***************"]
        
        for ip in test_ips:
            ip_int = self.validator.ip_to_int(ip)
            converted_back = self.validator.int_to_ip(ip_int)
            self.assertEqual(ip, converted_back, f"IP conversion failed for {ip}")
        
        # Test invalid IP addresses
        invalid_ips = ["256.1.1.1", "not.an.ip", "192.168.1.256"]
        for invalid_ip in invalid_ips:
            with self.assertRaises(IPAddressError):
                self.validator.ip_to_int(invalid_ip)

        # Test edge case that might not raise IPAddressError
        try:
            self.validator.ip_to_int("192.168.1")  # This might be valid in some systems
        except IPAddressError:
            pass  # Expected

        try:
            self.validator.ip_to_int("")  # Empty string
        except IPAddressError:
            pass  # Expected
        
        # Test invalid integers
        with self.assertRaises(IPAddressError):
            self.validator.int_to_ip(-1)
        with self.assertRaises(IPAddressError):
            self.validator.int_to_ip(2**32)
    
    def test_cidr_parsing(self):
        """Test CIDR parsing and IP matching."""
        # Test valid CIDR blocks
        test_cases = [
            ("127.0.0.1", "127.0.0.1/32", True),
            ("*********", "127.0.0.1/32", False),
            ("***********00", "***********/24", True),
            ("*************", "***********/24", False),
            ("*******", "0.0.0.0/0", True),
            ("127.0.0.1", "0.0.0.0/0", True),
        ]
        
        for ip, cidr, expected in test_cases:
            result = self.validator.is_ip_in_cidr(ip, cidr)
            self.assertEqual(result, expected, 
                           f"CIDR check failed: {ip} in {cidr} should be {expected}")
        
        # Test invalid CIDR formats
        with self.assertRaises(CIDRError):
            self.validator._get_cidr_network("invalid/cidr")
        
        # Test caching
        network1 = self.validator._get_cidr_network("***********/24")
        network2 = self.validator._get_cidr_network("***********/24")
        self.assertIs(network1, network2, "CIDR caching should return same object")
    
    def test_policy_validation(self):
        """Test policy configuration validation."""
        # Test valid configuration
        self.validator._validate_policy_config(self.sample_config)  # Should not raise
        
        # Test invalid configurations
        invalid_configs = [
            {},  # Missing 'ports' key
            {'ports': 'not_a_list'},  # 'ports' not a list
            {'ports': [{'invalid': 'config'}]},  # Missing 'ports' in port config
            {'ports': [{'ports': ['80'], 'cidr': {'cidr_allow_list': ['invalid/cidr']}}]}
        ]
        
        for invalid_config in invalid_configs:
            with self.assertRaises((PolicyValidationError, CIDRError)):
                self.validator._validate_policy_config(invalid_config)
    
    def test_connection_checking(self):
        """Test connection policy checking."""
        # Load sample policies
        self.validator._parse_policies(self.sample_config)
        
        # Test cases: (process, ip, port, expected_allowed, expected_reason_contains)
        test_cases = [
            ("curl", "127.0.0.1", 80, True, "allow"),
            ("nc", "127.0.0.1", 80, False, "deny"),
            ("curl", "*******", 80, False, "deny"),
            ("unknown", "127.0.0.1", 80, False, "not in allow"),
            ("ssh", "*******", 22, True, "allow"),
            ("curl", "127.0.0.1", 9999, False, "not in policy"),
            ("", "127.0.0.1", 80, False, "Invalid process"),
            ("curl", "invalid-ip", 80, False, "Invalid IP"),
            ("curl", "127.0.0.1", 0, False, "Invalid port"),
        ]
        
        for process, ip, port, expected_allowed, reason_contains in test_cases:
            allowed, reason = self.validator.check_connection(process, ip, port)
            self.assertEqual(allowed, expected_allowed, 
                           f"Connection check failed for {process}@{ip}:{port}")
            self.assertIn(reason_contains.lower(), reason.lower(),
                         f"Reason should contain '{reason_contains}' for {process}@{ip}:{port}")
    
    def test_policy_loading(self):
        """Test policy loading from file."""
        # Create temporary policy file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(self.sample_config, f)
            temp_file = f.name
        
        try:
            # Test successful loading
            validator = YAMLPolicyValidator(temp_file)
            result = validator.load_policy()
            self.assertTrue(result, "Policy loading should succeed")
            self.assertEqual(len(validator.policies), 3, "Should load 3 port policies")
            
            # Test file not found
            validator_missing = YAMLPolicyValidator("nonexistent.yaml")
            result = validator_missing.load_policy()
            self.assertFalse(result, "Loading nonexistent file should fail")
            
        finally:
            os.unlink(temp_file)
    
    def test_edge_cases(self):
        """Test various edge cases and error conditions."""
        # Test with empty policies
        empty_validator = YAMLPolicyValidator()
        allowed, reason = empty_validator.check_connection("test", "127.0.0.1", 80)
        self.assertFalse(allowed)
        
        # Test policy summary with no policies
        empty_validator.show_policy_summary()  # Should not crash
        
        # Test run method with invalid policy file
        invalid_validator = YAMLPolicyValidator("nonexistent.yaml")
        result = invalid_validator.run()
        self.assertFalse(result)


class TestIntegration(unittest.TestCase):
    """Integration tests for the complete system."""
    
    def test_full_workflow(self):
        """Test the complete workflow from loading to validation."""
        # This test requires the actual policy_config.yaml file
        if os.path.exists("policy_config.yaml"):
            validator = YAMLPolicyValidator()
            success = validator.load_policy()
            self.assertTrue(success, "Should load real policy file")
            
            if success:
                # Test some basic connections
                allowed, _ = validator.check_connection("curl", "127.0.0.1", 80)
                # Result depends on actual policy, just ensure no exceptions
                self.assertIsInstance(allowed, bool)


if __name__ == '__main__':
    # Run the tests
    unittest.main(verbosity=2)
