#!/usr/bin/env python3
"""
绝对路径匹配实现演示

这个脚本详细演示了绝对路径匹配的完整实现过程，包括：
1. 路径获取
2. 哈希计算
3. BPF Map键值构造
4. 匹配逻辑模拟
"""

import os
import sys
from typing import Dict, List, Optional, Tuple

# Add the project root to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.bpf_network_policy.utils import (
    ProcessUtils, StringUtils, process_utils, string_utils
)

class PathMatchingDemo:
    """路径匹配演示类"""
    
    def __init__(self):
        # 模拟BPF Map
        self.process_path_allow_map: Dict[int, int] = {}
        self.process_path_deny_map: Dict[int, int] = {}
        self.pid_path_cache: Dict[int, int] = {}
        self.port_policy_map: Dict[int, int] = {80: 1, 443: 1, 22: 2}
        
    def demonstrate_path_acquisition(self):
        """演示路径获取过程"""
        print("=== 1. 路径获取演示 ===\n")
        
        current_pid = os.getpid()
        print(f"当前进程 PID: {current_pid}")
        
        # 方法1：通过 /proc/PID/exe
        try:
            exe_link = f"/proc/{current_pid}/exe"
            if os.path.exists(exe_link):
                real_path = os.readlink(exe_link)
                print(f"方法1 (/proc/PID/exe): {real_path}")
        except Exception as e:
            print(f"方法1 失败: {e}")
        
        # 方法2：通过工具类
        path_from_utils = process_utils.get_process_path(current_pid)
        print(f"方法2 (工具类): {path_from_utils}")
        
        # 方法3：通过 /proc/PID/cmdline
        try:
            cmdline_path = f"/proc/{current_pid}/cmdline"
            if os.path.exists(cmdline_path):
                with open(cmdline_path, 'r') as f:
                    cmdline = f.read().strip('\x00')
                    if cmdline:
                        exe_path = cmdline.split('\x00')[0]
                        print(f"方法3 (/proc/PID/cmdline): {exe_path}")
        except Exception as e:
            print(f"方法3 失败: {e}")
        
        print()
    
    def demonstrate_hash_calculation(self):
        """演示哈希计算过程"""
        print("=== 2. 哈希计算演示 ===\n")
        
        test_paths = [
            "/usr/bin/curl",
            "/usr/bin/wget", 
            "/usr/bin/python3",
            "/tmp/curl",
            "/tmp/malicious_tool",
            "/opt/google/chrome/chrome",
            "/snap/firefox/current/usr/lib/firefox/firefox"
        ]
        
        print("路径哈希计算结果:")
        print(f"{'路径':<50} {'哈希值':<12} {'二进制表示'}")
        print("-" * 80)
        
        for path in test_paths:
            hash_val = string_utils.hash_long_string(path)
            binary_repr = format(hash_val, '032b')
            print(f"{path:<50} {hash_val:<12} {binary_repr}")
        
        print()
        
        # 演示哈希算法步骤
        print("哈希算法步骤演示 (以 '/usr/bin/curl' 为例):")
        path = "/usr/bin/curl"
        hash_val = 5381
        print(f"初始值: {hash_val}")
        
        for i, c in enumerate(path[:10]):  # 只显示前10个字符
            old_hash = hash_val
            hash_val = ((hash_val << 5) + hash_val + ord(c)) & 0xFFFFFFFF
            print(f"字符 '{c}' (ASCII {ord(c)}): {old_hash} -> {hash_val}")
        
        print(f"最终哈希值: {hash_val}")
        print()
    
    def demonstrate_bpf_key_construction(self):
        """演示BPF键值构造"""
        print("=== 3. BPF键值构造演示 ===\n")
        
        # 模拟策略配置
        policy_config = {
            1: {  # 策略ID 1 (端口80, 443)
                "allow_paths": ["/usr/bin/curl", "/usr/bin/wget", "/usr/bin/firefox"],
                "deny_paths": ["/tmp/curl", "/tmp/wget"]
            },
            2: {  # 策略ID 2 (端口22)
                "allow_paths": ["/usr/bin/ssh", "/usr/bin/scp"],
                "deny_paths": ["/tmp/ssh"]
            }
        }
        
        print("BPF Map键值构造:")
        print(f"{'策略ID':<8} {'路径':<30} {'路径哈希':<12} {'BPF键值':<15} {'类型'}")
        print("-" * 85)
        
        for policy_id, config in policy_config.items():
            # 处理允许列表
            for path in config["allow_paths"]:
                path_hash = string_utils.hash_long_string(path)
                bpf_key = (policy_id << 32) | path_hash
                self.process_path_allow_map[bpf_key] = 1
                print(f"{policy_id:<8} {path:<30} {path_hash:<12} {bpf_key:<15} 允许")
            
            # 处理拒绝列表
            for path in config["deny_paths"]:
                path_hash = string_utils.hash_long_string(path)
                bpf_key = (policy_id << 32) | path_hash
                self.process_path_deny_map[bpf_key] = 1
                print(f"{policy_id:<8} {path:<30} {path_hash:<12} {bpf_key:<15} 拒绝")
        
        print()
    
    def simulate_path_matching(self, pid: int, path: str, port: int) -> Tuple[bool, str]:
        """模拟路径匹配过程"""
        print(f"=== 模拟匹配: PID={pid}, 路径={path}, 端口={port} ===")
        
        # 1. 计算路径哈希并更新缓存
        path_hash = string_utils.hash_long_string(path)
        self.pid_path_cache[pid] = path_hash
        print(f"1. 路径哈希: {path_hash}")
        
        # 2. 获取策略ID
        policy_id = self.port_policy_map.get(port)
        if not policy_id:
            return False, f"端口 {port} 没有对应的策略"
        print(f"2. 策略ID: {policy_id}")
        
        # 3. 构造BPF键
        bpf_key = (policy_id << 32) | path_hash
        print(f"3. BPF键: {bpf_key} (0x{bpf_key:016x})")
        
        # 4. 检查黑名单
        if bpf_key in self.process_path_deny_map:
            return False, "路径在黑名单中"
        print("4. 黑名单检查: 通过")
        
        # 5. 检查白名单
        if bpf_key in self.process_path_allow_map:
            return True, "路径在白名单中"
        
        return False, "路径不在白名单中"
    
    def demonstrate_matching_scenarios(self):
        """演示各种匹配场景"""
        print("=== 4. 匹配场景演示 ===\n")
        
        test_scenarios = [
            # (PID, 路径, 端口, 预期结果)
            (1001, "/usr/bin/curl", 80, True),      # 合法curl访问HTTP
            (1002, "/tmp/curl", 80, False),         # 恶意curl访问HTTP
            (1003, "/usr/bin/wget", 80, True),      # 合法wget访问HTTP
            (1004, "/tmp/wget", 80, False),         # 恶意wget访问HTTP
            (1005, "/usr/bin/ssh", 22, True),       # 合法SSH客户端
            (1006, "/tmp/ssh", 22, False),          # 恶意SSH客户端
            (1007, "/usr/bin/python3", 80, False),  # Python访问HTTP（不在白名单）
            (1008, "/usr/bin/firefox", 443, True),  # Firefox访问HTTPS
        ]
        
        print("匹配结果:")
        print(f"{'PID':<6} {'路径':<30} {'端口':<6} {'结果':<6} {'原因'}")
        print("-" * 70)
        
        for pid, path, port, expected in test_scenarios:
            allowed, reason = self.simulate_path_matching(pid, path, port)
            result_str = "✅允许" if allowed else "❌拒绝"
            expected_str = "✅" if expected else "❌"
            match_str = "✓" if (allowed == expected) else "✗"
            
            print(f"{pid:<6} {path:<30} {port:<6} {result_str:<6} {reason}")
            print(f"      预期: {expected_str}, 实际: {'✅' if allowed else '❌'}, 匹配: {match_str}")
            print()
    
    def demonstrate_performance_characteristics(self):
        """演示性能特征"""
        print("=== 5. 性能特征演示 ===\n")
        
        import time
        
        # 测试哈希计算性能
        test_paths = [f"/usr/bin/test_program_{i}" for i in range(1000)]
        
        start_time = time.time()
        hashes = [string_utils.hash_long_string(path) for path in test_paths]
        hash_time = time.time() - start_time
        
        print(f"哈希计算性能:")
        print(f"  路径数量: {len(test_paths)}")
        print(f"  总时间: {hash_time:.4f} 秒")
        print(f"  平均时间: {hash_time/len(test_paths)*1000:.4f} 毫秒/路径")
        
        # 测试查找性能
        lookup_map = {(1 << 32) | h: 1 for h in hashes}
        
        start_time = time.time()
        for h in hashes:
            key = (1 << 32) | h
            _ = lookup_map.get(key)
        lookup_time = time.time() - start_time
        
        print(f"\nMap查找性能:")
        print(f"  查找次数: {len(hashes)}")
        print(f"  总时间: {lookup_time:.4f} 秒")
        print(f"  平均时间: {lookup_time/len(hashes)*1000000:.2f} 微秒/查找")
        
        # 内存使用估算
        map_size = len(self.process_path_allow_map) + len(self.process_path_deny_map)
        memory_usage = map_size * 16  # 每个条目约16字节
        
        print(f"\n内存使用:")
        print(f"  Map条目数: {map_size}")
        print(f"  估算内存: {memory_usage} 字节 ({memory_usage/1024:.1f} KB)")
        print()

def main():
    """主函数"""
    print("绝对路径匹配实现演示")
    print("=" * 50)
    print()
    
    demo = PathMatchingDemo()
    
    try:
        demo.demonstrate_path_acquisition()
        demo.demonstrate_hash_calculation()
        demo.demonstrate_bpf_key_construction()
        demo.demonstrate_matching_scenarios()
        demo.demonstrate_performance_characteristics()
        
        print("演示完成！")
        print("\n关键要点:")
        print("1. 路径通过 /proc/PID/exe 获取，确保准确性")
        print("2. 使用djb2哈希算法，平衡性能和冲突率")
        print("3. BPF键由策略ID和路径哈希组成，支持多策略")
        print("4. 匹配过程：黑名单 -> 白名单 -> 进程名备用")
        print("5. 性能优秀：微秒级查找，KB级内存使用")
        
    except Exception as e:
        print(f"演示过程中出现错误: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
