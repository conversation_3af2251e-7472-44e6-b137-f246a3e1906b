#!/usr/bin/env python3
"""
Process Path-based BPF Network Policy Demo

This script demonstrates how to use process absolute paths for network policy
control, providing more precise security than process name-based control.
"""

import os
import sys
import time
import glob
from typing import Dict, List, Optional

# Add the project root to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.bpf_network_policy.utils import (
    ProcessUtils, StringUtils, process_utils, string_utils
)

def demonstrate_process_path_detection():
    """Demonstrate process path detection capabilities."""
    print("=== Process Path Detection Demo ===\n")
    
    # Get current process info
    current_pid = os.getpid()
    current_path = process_utils.get_process_path(current_pid)
    
    print(f"Current Process:")
    print(f"  PID: {current_pid}")
    print(f"  Path: {current_path}")
    print(f"  Path Hash: {string_utils.hash_long_string(current_path) if current_path else 'N/A'}")
    print()
    
    # Get some system processes by scanning /proc
    print("System Processes:")

    # Get a few running processes
    proc_dirs = glob.glob('/proc/[0-9]*')[:10]  # Limit to first 10 processes

    for proc_dir in proc_dirs:
        try:
            pid = int(os.path.basename(proc_dir))

            # Skip kernel threads and current process
            if pid <= 2 or pid == current_pid:
                continue

            # Get process name from comm file
            try:
                with open(f"{proc_dir}/comm", 'r') as f:
                    name = f.read().strip()
            except:
                continue

            # Get process path
            path = process_utils.get_process_path(pid)

            if path and name:
                print(f"  {name} (PID: {pid})")
                print(f"    Path: {path}")
                print(f"    Path Hash: {string_utils.hash_long_string(path)}")
                print()

        except (ValueError, OSError, PermissionError):
            continue

def demonstrate_path_vs_name_hashing():
    """Demonstrate the difference between path and name hashing."""
    print("=== Path vs Name Hashing Demo ===\n")
    
    test_cases = [
        ("/usr/bin/curl", "curl"),
        ("/usr/bin/wget", "wget"),
        ("/usr/bin/python3", "python3"),
        ("/opt/google/chrome/chrome", "chrome"),
        ("/snap/firefox/current/usr/lib/firefox/firefox", "firefox"),
        ("/tmp/malicious_tool", "malicious_tool"),
    ]
    
    print("Process Name vs Path Hashing:")
    print(f"{'Path':<50} {'Name':<20} {'Path Hash':<12} {'Name Hash':<12}")
    print("-" * 100)
    
    for path, name in test_cases:
        path_hash = string_utils.hash_long_string(path)
        name_hash = string_utils.hash_string(name)
        print(f"{path:<50} {name:<20} {path_hash:<12} {name_hash:<12}")
    
    print()

def demonstrate_security_benefits():
    """Demonstrate security benefits of path-based control."""
    print("=== Security Benefits Demo ===\n")
    
    print("Scenario: Process Name Spoofing Attack")
    print("An attacker creates a malicious binary with a trusted name:")
    print()
    
    # Simulate legitimate vs malicious processes
    legitimate_cases = [
        ("/usr/bin/curl", "curl"),
        ("/usr/bin/ssh", "ssh"),
        ("/usr/sbin/nginx", "nginx"),
    ]
    
    malicious_cases = [
        ("/tmp/curl", "curl"),           # Malicious curl in /tmp
        ("/home/<USER>/ssh", "ssh"),   # Malicious ssh in user dir
        ("/var/tmp/nginx", "nginx"),     # Malicious nginx in temp
    ]
    
    print("Legitimate Processes:")
    for path, name in legitimate_cases:
        path_hash = string_utils.hash_long_string(path)
        name_hash = string_utils.hash_string(name)
        print(f"  {name}: {path}")
        print(f"    Name Hash: {name_hash} (would be allowed by name-only policy)")
        print(f"    Path Hash: {path_hash} (would be allowed by path policy)")
        print()
    
    print("Malicious Processes (same names, different paths):")
    for path, name in malicious_cases:
        path_hash = string_utils.hash_long_string(path)
        name_hash = string_utils.hash_string(name)
        print(f"  {name}: {path}")
        print(f"    Name Hash: {name_hash} (would be ALLOWED by name-only policy ❌)")
        print(f"    Path Hash: {path_hash} (would be BLOCKED by path policy ✅)")
        print()
    
    print("Conclusion: Path-based control prevents process name spoofing attacks!")
    print()

def demonstrate_policy_mapping():
    """Demonstrate how processes would be mapped in BPF policy."""
    print("=== BPF Policy Mapping Demo ===\n")
    
    # Simulate policy ID 1 for port 80
    policy_id = 1
    
    allowed_paths = [
        "/usr/bin/curl",
        "/usr/bin/wget",
        "/usr/bin/firefox",
        "/usr/sbin/nginx",
    ]
    
    denied_paths = [
        "/tmp/malicious_tool",
        "/var/tmp/suspicious_binary",
        "/home/<USER>/fake_curl",
    ]
    
    print(f"Policy ID: {policy_id} (for port 80)")
    print()
    
    print("Allowed Process Paths:")
    for path in allowed_paths:
        path_hash = string_utils.hash_long_string(path)
        bpf_key = (policy_id << 32) | path_hash
        print(f"  {path}")
        print(f"    Path Hash: {path_hash}")
        print(f"    BPF Map Key: {bpf_key} -> 1 (allowed)")
        print()
    
    print("Denied Process Paths:")
    for path in denied_paths:
        path_hash = string_utils.hash_long_string(path)
        bpf_key = (policy_id << 32) | path_hash
        print(f"  {path}")
        print(f"    Path Hash: {path_hash}")
        print(f"    BPF Map Key: {bpf_key} -> 1 (denied)")
        print()

def main():
    """Main demo function."""
    print("Process Path-based BPF Network Policy Demo")
    print("=" * 50)
    print()
    
    try:
        demonstrate_process_path_detection()
        demonstrate_path_vs_name_hashing()
        demonstrate_security_benefits()
        demonstrate_policy_mapping()
        
        print("Demo completed successfully!")
        print()
        print("Key Takeaways:")
        print("1. Process paths provide more precise control than process names")
        print("2. Path-based policies prevent process name spoofing attacks")
        print("3. Both name and path policies can be used together for flexibility")
        print("4. Path hashing allows efficient BPF map lookups")
        print("5. Absolute paths should be used for security")
        
    except Exception as e:
        print(f"Demo failed with error: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
