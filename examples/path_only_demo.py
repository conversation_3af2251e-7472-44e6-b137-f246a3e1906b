#!/usr/bin/env python3
"""
进程路径专用网络策略控制演示

展示如何使用专门的进程路径管控系统，不包含进程名管控
"""

import os
import sys
import time

# Add the project root to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.bpf_network_policy.core.path_only_policy_control import PathOnlyPolicyController

def demo_path_only_policy():
    """演示进程路径专用策略控制"""
    print("进程路径专用网络策略控制演示")
    print("=" * 50)
    
    # 检查权限
    if os.geteuid() != 0:
        print("❌ 需要root权限运行此演示")
        print("请使用: sudo python3 examples/path_only_demo.py")
        return False
    
    try:
        # 创建路径专用策略控制器
        controller = PathOnlyPolicyController(
            policy_file="config/path_only_policy.yaml"
        )
        
        print("1. 加载策略配置...")
        if not controller.load_policy():
            print("❌ 策略加载失败")
            return False
        
        print("\n2. 编译和加载BPF程序...")
        if not controller.load_bpf_program():
            print("❌ BPF程序加载失败")
            return False
        
        print("\n3. 配置BPF策略映射...")
        if not controller.configure_policies():
            print("❌ 策略配置失败")
            return False
        
        print("\n4. 显示策略摘要...")
        controller.show_policy_summary()
        
        print("\n5. 显示统计信息...")
        controller.show_statistics()
        
        print("\n✅ 进程路径专用策略控制器启动成功！")
        print("\n特点:")
        print("- ✅ 只基于进程路径进行管控")
        print("- ✅ 不包含进程名管控")
        print("- ✅ 支持路径白名单和黑名单")
        print("- ✅ 支持IP白名单和黑名单")
        print("- ✅ 支持端口级别的策略控制")
        
        print("\n注意: 当前只是加载和配置了策略，实际拦截需要附加到网络钩子")
        
        return True
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_policy_examples():
    """显示策略配置示例"""
    print("\n=== 策略配置示例 ===")
    
    print("\n1. HTTP/HTTPS 端口策略:")
    print("   允许的进程路径:")
    print("   - /usr/bin/curl")
    print("   - /usr/bin/wget")
    print("   - /usr/bin/firefox")
    print("   - /usr/bin/python3")
    print("   拒绝的进程路径:")
    print("   - /usr/bin/nc")
    print("   - /tmp/malicious")
    
    print("\n2. SSH 端口策略:")
    print("   允许的进程路径:")
    print("   - /usr/bin/ssh")
    print("   - /usr/bin/scp")
    print("   - /usr/bin/sftp")
    print("   拒绝的进程路径:")
    print("   - /usr/bin/nc")
    print("   - /tmp/backdoor")
    
    print("\n3. 数据库端口策略:")
    print("   允许的进程路径:")
    print("   - /usr/bin/mysql")
    print("   - /usr/bin/python3")
    print("   - /opt/myapp/bin/myapp")
    print("   拒绝的进程路径:")
    print("   - /usr/bin/nc")
    print("   - /tmp/exploit")

def show_usage_guide():
    """显示使用指南"""
    print("\n=== 使用指南 ===")
    
    print("\n1. 配置文件位置:")
    print("   config/path_only_policy.yaml")
    
    print("\n2. BPF程序位置:")
    print("   src/bpf_network_policy/bpf/bpf_path_only_control.c")
    
    print("\n3. 控制程序位置:")
    print("   src/bpf_network_policy/core/path_only_policy_control.py")
    
    print("\n4. 运行方式:")
    print("   # 使用默认配置")
    print("   sudo python3 -m src.bpf_network_policy.core.path_only_policy_control")
    print("   ")
    print("   # 使用自定义配置")
    print("   sudo python3 -m src.bpf_network_policy.core.path_only_policy_control my_policy.yaml")
    
    print("\n5. 配置格式:")
    print("   ports:")
    print("     - ports: [\"80\", \"443\"]")
    print("       process_path:")
    print("         process_path_allow_list:")
    print("           - \"/usr/bin/curl\"")
    print("           - \"/usr/bin/wget\"")
    print("         process_path_deny_list:")
    print("           - \"/usr/bin/nc\"")
    print("       cidr:")
    print("         cidr_allow_list:")
    print("           - \"0.0.0.0/0\"")
    print("         cidr_deny_list: []")

def main():
    """主函数"""
    print("欢迎使用进程路径专用网络策略控制系统！")
    print("=" * 60)
    
    # 显示策略示例
    show_policy_examples()
    
    # 显示使用指南
    show_usage_guide()
    
    # 询问是否运行演示
    print("\n" + "=" * 60)
    response = input("是否运行实际演示? (需要root权限) [y/N]: ").strip().lower()
    
    if response in ['y', 'yes']:
        success = demo_path_only_policy()
        if success:
            print("\n🎉 演示成功完成！")
            print("你现在可以:")
            print("1. 修改 config/path_only_policy.yaml 来自定义策略")
            print("2. 运行控制程序来应用策略")
            print("3. 将BPF程序附加到cgroup或其他网络钩子来实现实际拦截")
        else:
            print("\n❌ 演示失败")
            return 1
    else:
        print("\n演示已跳过。你可以随时运行:")
        print("sudo python3 examples/path_only_demo.py")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
