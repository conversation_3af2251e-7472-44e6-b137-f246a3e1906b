#!/usr/bin/env python3
"""
使用tracepoint测试eBPF网络监控功能
这个测试不直接拦截，但可以监控网络连接并验证eBPF程序的工作
"""

import os
import sys
import time
import socket
import threading
from bcc import BPF

def test_network_monitoring():
    """测试网络监控功能"""
    print("=== 测试eBPF网络监控 ===")
    
    # 检查权限
    if os.geteuid() != 0:
        print("❌ 需要root权限")
        return False
    
    try:
        # 使用tracepoint监控网络连接
        bpf_code = """
#include <uapi/linux/ptrace.h>
#include <linux/socket.h>
#include <linux/in.h>

// 连接事件结构
struct connect_event {
    u32 pid;
    u32 uid;
    u16 dport;
    u32 daddr;
    char comm[16];
};

// 事件输出
BPF_PERF_OUTPUT(connect_events);

// 统计信息
BPF_HASH(connection_stats, u32, u64, 10);

// 策略检查结果
BPF_HASH(policy_decisions, u32, u64, 10);

// 简单的字符串哈希函数
static inline u32 hash_string(char *str) {
    u32 hash = 5381;
    for (int i = 0; i < 16 && str[i] != 0; i++) {
        hash = ((hash << 5) + hash) + str[i];
    }
    return hash;
}

// 监控connect系统调用
int trace_connect_entry(struct pt_regs *ctx) {
    struct connect_event event = {};
    
    // 获取进程信息
    event.pid = bpf_get_current_pid_tgid() >> 32;
    event.uid = bpf_get_current_uid_gid() & 0xFFFFFFFF;
    bpf_get_current_comm(&event.comm, sizeof(event.comm));
    
    // 获取socket地址信息
    struct sockaddr *addr = (struct sockaddr *)PT_REGS_PARM2(ctx);
    if (addr) {
        struct sockaddr_in *addr_in = (struct sockaddr_in *)addr;
        if (addr_in->sin_family == AF_INET) {
            event.dport = bpf_ntohs(addr_in->sin_port);
            event.daddr = addr_in->sin_addr.s_addr;
        }
    }
    
    // 更新统计信息
    u32 total_key = 0;
    u64 *total_count = connection_stats.lookup(&total_key);
    if (total_count) {
        (*total_count)++;
    } else {
        u64 init_val = 1;
        connection_stats.update(&total_key, &init_val);
    }
    
    // 模拟策略检查
    u32 comm_hash = hash_string(event.comm);
    u32 policy_decision = 1; // 默认允许
    
    // 简单的策略：拒绝nc连接到8080端口
    if (event.dport == 8080) {
        u32 nc_hash = 0x005978d6; // "nc"的哈希值
        if (comm_hash == nc_hash) {
            policy_decision = 0; // 拒绝
            
            // 更新拒绝统计
            u32 denied_key = 2;
            u64 *denied_count = policy_decisions.lookup(&denied_key);
            if (denied_count) {
                (*denied_count)++;
            } else {
                u64 init_val = 1;
                policy_decisions.update(&denied_key, &init_val);
            }
        }
    }
    
    if (policy_decision == 1) {
        // 更新允许统计
        u32 allowed_key = 1;
        u64 *allowed_count = policy_decisions.lookup(&allowed_key);
        if (allowed_count) {
            (*allowed_count)++;
        } else {
            u64 init_val = 1;
            policy_decisions.update(&allowed_key, &init_val);
        }
    }
    
    // 发送事件到用户空间
    connect_events.perf_submit(ctx, &event, sizeof(event));
    
    return 0;
}
"""
        
        print("编译eBPF程序...")
        bpf = BPF(text=bpf_code)
        print("✓ eBPF程序编译成功")
        
        # 附加到connect系统调用
        bpf.attach_kprobe(event="__sys_connect", fn_name="trace_connect_entry")
        print("✓ eBPF程序附加到connect系统调用")
        
        # 设置事件处理器
        events = []
        def handle_event(cpu, data, size):
            event = bpf["connect_events"].event(data)
            events.append({
                'pid': event.pid,
                'uid': event.uid,
                'comm': event.comm.decode('utf-8', 'replace'),
                'dport': event.dport,
                'daddr': socket.inet_ntoa(event.daddr.to_bytes(4, 'little'))
            })
        
        bpf["connect_events"].open_perf_buffer(handle_event)
        
        print("✓ 开始监控网络连接...")
        
        # 在后台进行一些网络连接测试
        def make_test_connections():
            time.sleep(1)  # 等待监控开始
            
            test_connections = [
                ("127.0.0.1", 80, "HTTP连接"),
                ("*******", 53, "DNS查询"),
                ("127.0.0.1", 8080, "8080端口连接"),
            ]
            
            for host, port, desc in test_connections:
                try:
                    print(f"  尝试连接: {desc} ({host}:{port})")
                    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                    sock.settimeout(1)
                    sock.connect_ex((host, port))
                    sock.close()
                    time.sleep(0.5)
                except Exception as e:
                    print(f"    连接异常: {e}")
        
        # 启动测试连接线程
        test_thread = threading.Thread(target=make_test_connections)
        test_thread.start()
        
        # 监控事件
        print("\n监控网络连接事件...")
        start_time = time.time()
        while time.time() - start_time < 10:  # 监控10秒
            try:
                bpf.perf_buffer_poll(timeout=100)
            except KeyboardInterrupt:
                break
        
        test_thread.join()
        
        # 显示捕获的事件
        print(f"\n捕获到 {len(events)} 个连接事件:")
        for i, event in enumerate(events):
            print(f"  {i+1}. PID:{event['pid']} 进程:{event['comm']} -> {event['daddr']}:{event['dport']}")
        
        # 显示统计信息
        connection_stats = bpf.get_table("connection_stats")
        policy_decisions = bpf.get_table("policy_decisions")

        # 使用正确的方式读取BPF表
        total_connections = 0
        allowed_connections = 0
        denied_connections = 0

        try:
            total_connections = connection_stats[connection_stats.Key(0)].value
        except KeyError:
            pass

        try:
            allowed_connections = policy_decisions[policy_decisions.Key(1)].value
        except KeyError:
            pass

        try:
            denied_connections = policy_decisions[policy_decisions.Key(2)].value
        except KeyError:
            pass
        
        print(f"\n=== 统计信息 ===")
        print(f"总连接数: {total_connections}")
        print(f"允许连接: {allowed_connections}")
        print(f"拒绝连接: {denied_connections}")
        
        # 清理
        bpf.detach_kprobe(event="__sys_connect")
        print("✓ eBPF程序已分离")
        
        return len(events) > 0 and total_connections > 0
        
    except Exception as e:
        print(f"❌ 网络监控测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("eBPF网络监控测试")
    print("=" * 40)
    
    success = test_network_monitoring()
    
    if success:
        print("\n🎉 eBPF网络监控测试成功！")
        print("✓ eBPF程序能够监控网络连接")
        print("✓ eBPF程序能够执行策略检查逻辑")
        print("✓ eBPF程序能够统计连接信息")
        print("\n注意: 这个测试验证了eBPF的监控能力")
        print("      实际的拦截需要使用cgroup或其他机制")
    else:
        print("\n❌ eBPF网络监控测试失败")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
