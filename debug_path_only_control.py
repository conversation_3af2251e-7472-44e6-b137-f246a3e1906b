#!/usr/bin/env python3
"""
调试 bpf_path_only_control.c 的配置问题
"""

import os
import sys
from bcc import BPF

def debug_bpf_path_only():
    """调试BPF路径专用控制"""
    print("=== 调试 bpf_path_only_control.c ===")
    
    # 检查权限
    if os.geteuid() != 0:
        print("❌ 需要root权限")
        return False
    
    bpf_file = "src/bpf_network_policy/bpf/bpf_path_only_control.c"
    
    if not os.path.exists(bpf_file):
        print(f"❌ BPF文件不存在: {bpf_file}")
        return False
    
    try:
        print(f"1. 编译BPF程序: {bpf_file}")
        bpf = BPF(src_file=bpf_file)
        print("✅ 编译成功")
        
        print("\n2. 检查BPF表...")
        expected_tables = [
            'port_policy_map',
            'process_path_allow_map', 
            'process_path_deny_map',
            'cidr_allow_map',
            'cidr_deny_map',
            'policy_stats'
        ]
        
        tables = {}
        for table_name in expected_tables:
            try:
                table = bpf.get_table(table_name)
                tables[table_name] = table
                print(f"  ✅ {table_name}")
            except KeyError as e:
                print(f"  ❌ {table_name}: {e}")
        
        print(f"\n3. 测试基本表操作...")
        
        # 测试端口策略表
        if 'port_policy_map' in tables:
            try:
                port_map = tables['port_policy_map']
                print("  测试端口策略表...")
                port_map[port_map.Key(80)] = port_map.Leaf(1)
                print("    ✅ 端口80 -> 策略1")
                
                # 读取验证
                count = 0
                for key, value in port_map.items():
                    print(f"    读取: 端口{key.value} -> 策略{value.value}")
                    count += 1
                print(f"    ✅ 端口表有 {count} 条记录")
                
            except Exception as e:
                print(f"    ❌ 端口表操作失败: {e}")
                import traceback
                traceback.print_exc()
        
        # 测试进程路径白名单表
        if 'process_path_allow_map' in tables:
            try:
                path_allow_map = tables['process_path_allow_map']
                print("  测试进程路径白名单表...")
                
                # 哈希函数
                def hash_long_string(s):
                    hash_val = 5381
                    for i, c in enumerate(s):
                        if i >= 256:
                            break
                        hash_val = ((hash_val << 5) + hash_val + ord(c)) & 0xFFFFFFFF
                    return hash_val
                
                # 测试路径
                test_path = "/usr/bin/python3"
                path_hash = hash_long_string(test_path)
                policy_id = 1
                key = (policy_id << 32) | path_hash
                
                print(f"    路径: {test_path}")
                print(f"    哈希: 0x{path_hash:x}")
                print(f"    键值: 0x{key:x}")
                
                path_allow_map[path_allow_map.Key(key)] = path_allow_map.Leaf(1)
                print("    ✅ 添加路径白名单成功")
                
                # 读取验证
                count = 0
                for key_obj, value in path_allow_map.items():
                    policy_id_read = key_obj.value >> 32
                    path_hash_read = key_obj.value & 0xFFFFFFFF
                    print(f"    读取: 策略{policy_id_read}, 路径哈希0x{path_hash_read:x}")
                    count += 1
                print(f"    ✅ 路径白名单表有 {count} 条记录")
                
            except Exception as e:
                print(f"    ❌ 路径白名单表操作失败: {e}")
                import traceback
                traceback.print_exc()
        
        # 测试IP白名单表
        if 'cidr_allow_map' in tables:
            try:
                cidr_allow_map = tables['cidr_allow_map']
                print("  测试IP白名单表...")
                
                def ip_to_int(ip_str):
                    parts = ip_str.split('.')
                    return (int(parts[0]) << 24) | (int(parts[1]) << 16) | (int(parts[2]) << 8) | int(parts[3])
                
                test_ip = "127.0.0.1"
                ip_int = ip_to_int(test_ip)
                policy_id = 1
                key = (policy_id << 32) | ip_int
                
                print(f"    IP: {test_ip}")
                print(f"    整数: 0x{ip_int:x}")
                print(f"    键值: 0x{key:x}")
                
                cidr_allow_map[cidr_allow_map.Key(key)] = cidr_allow_map.Leaf(1)
                print("    ✅ 添加IP白名单成功")
                
                # 读取验证
                count = 0
                for key_obj, value in cidr_allow_map.items():
                    policy_id_read = key_obj.value >> 32
                    ip_int_read = key_obj.value & 0xFFFFFFFF
                    print(f"    读取: 策略{policy_id_read}, IP整数0x{ip_int_read:x}")
                    count += 1
                print(f"    ✅ IP白名单表有 {count} 条记录")
                
            except Exception as e:
                print(f"    ❌ IP白名单表操作失败: {e}")
                import traceback
                traceback.print_exc()
        
        # 测试统计表
        if 'policy_stats' in tables:
            try:
                stats_map = tables['policy_stats']
                print("  测试统计表...")
                
                stats_map[stats_map.Key(0)] = stats_map.Leaf(100)  # 总数
                stats_map[stats_map.Key(1)] = stats_map.Leaf(80)   # 允许
                stats_map[stats_map.Key(2)] = stats_map.Leaf(20)   # 拒绝
                print("    ✅ 添加统计数据成功")
                
                # 读取验证
                for key_obj, value in stats_map.items():
                    stat_type = {0: "总数", 1: "允许", 2: "拒绝"}.get(key_obj.value, "未知")
                    print(f"    读取: {stat_type} = {value.value}")
                
            except Exception as e:
                print(f"    ❌ 统计表操作失败: {e}")
                import traceback
                traceback.print_exc()
        
        print("\n4. 综合配置测试...")
        try:
            # 配置完整的测试策略
            def hash_long_string(s):
                hash_val = 5381
                for i, c in enumerate(s):
                    if i >= 256:
                        break
                    hash_val = ((hash_val << 5) + hash_val + ord(c)) & 0xFFFFFFFF
                return hash_val
            
            def ip_to_int(ip_str):
                parts = ip_str.split('.')
                return (int(parts[0]) << 24) | (int(parts[1]) << 16) | (int(parts[2]) << 8) | int(parts[3])
            
            # 端口策略
            port_map = tables['port_policy_map']
            test_ports = {80: 1, 443: 1, 22: 2}
            for port, policy_id in test_ports.items():
                port_map[port_map.Key(port)] = port_map.Leaf(policy_id)
            print(f"  ✅ 配置端口策略: {list(test_ports.keys())}")
            
            # 路径白名单
            path_allow_map = tables['process_path_allow_map']
            allowed_paths = ["/usr/bin/python3", "/usr/bin/curl"]
            for path in allowed_paths:
                path_hash = hash_long_string(path)
                for policy_id in [1, 2]:
                    key = (policy_id << 32) | path_hash
                    path_allow_map[path_allow_map.Key(key)] = path_allow_map.Leaf(1)
            print(f"  ✅ 配置路径白名单: {allowed_paths}")
            
            # 路径黑名单
            path_deny_map = tables['process_path_deny_map']
            denied_paths = ["/usr/bin/nc"]
            for path in denied_paths:
                path_hash = hash_long_string(path)
                for policy_id in [1, 2]:
                    key = (policy_id << 32) | path_hash
                    path_deny_map[path_deny_map.Key(key)] = path_deny_map.Leaf(1)
            print(f"  ✅ 配置路径黑名单: {denied_paths}")
            
            # IP白名单
            cidr_allow_map = tables['cidr_allow_map']
            allowed_ips = ["127.0.0.1", "*******"]
            for ip_str in allowed_ips:
                ip_int = ip_to_int(ip_str)
                for policy_id in [1, 2]:
                    key = (policy_id << 32) | ip_int
                    cidr_allow_map[cidr_allow_map.Key(key)] = cidr_allow_map.Leaf(1)
            print(f"  ✅ 配置IP白名单: {allowed_ips}")
            
        except Exception as e:
            print(f"  ❌ 综合配置失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        print("\n5. 最终验证...")
        # 验证所有表的记录数
        for table_name, table in tables.items():
            try:
                count = sum(1 for _ in table.items())
                print(f"  {table_name}: {count} 条记录")
            except Exception as e:
                print(f"  {table_name}: 读取失败 - {e}")
        
        print("\n🎉 bpf_path_only_control.c 调试成功！")
        print("✅ BPF程序编译正常")
        print("✅ 所有表操作正常")
        print("✅ 策略配置正常")
        
        return True
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    success = debug_bpf_path_only()
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
