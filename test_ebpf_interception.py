#!/usr/bin/env python3
"""
eBPF网络拦截功能测试

这个脚本测试eBPF程序是否能够真正拦截网络连接
"""

import os
import sys
import time
import socket
import subprocess
from typing import Dict, List, Optional

def check_prerequisites():
    """检查运行前提条件"""
    print("=== 检查运行环境 ===")
    
    # 检查用户权限
    uid = os.getuid()
    print(f"当前用户UID: {uid}")
    
    if uid != 0:
        print("❌ 需要root权限来运行eBPF程序")
        print("请使用: sudo python3 test_ebpf_interception.py")
        return False
    
    # 检查BCC是否可用
    try:
        from bcc import BPF
        print("✓ BCC库可用")
    except ImportError as e:
        print(f"❌ BCC库不可用: {e}")
        print("请安装BCC: sudo apt-get install bpfcc-tools python3-bpfcc")
        return False
    
    # 检查内核版本
    try:
        with open('/proc/version', 'r') as f:
            kernel_version = f.read().strip()
        print(f"✓ 内核版本: {kernel_version}")
    except:
        print("⚠️  无法获取内核版本")
    
    # 检查cgroup v2是否可用
    cgroup_path = "/sys/fs/cgroup"
    if os.path.exists(cgroup_path):
        print(f"✓ cgroup文件系统可用: {cgroup_path}")
    else:
        print(f"❌ cgroup文件系统不可用: {cgroup_path}")
        return False
    
    return True

def test_simple_ebpf():
    """测试简单的eBPF程序"""
    print("\n=== 测试简单eBPF程序 ===")
    
    try:
        from bcc import BPF
        
        # 最简单的eBPF程序 - 只统计连接
        bpf_code = """
#include <uapi/linux/ptrace.h>
#include <linux/socket.h>

BPF_HASH(stats, u32, u64, 10);

int trace_connect(struct bpf_sock_addr *ctx) {
    u32 key = 0;
    u64 *count = stats.lookup(&key);
    if (count) {
        (*count)++;
    } else {
        u64 init_val = 1;
        stats.update(&key, &init_val);
    }
    return 1; // 允许所有连接
}
"""
        
        # 编译eBPF程序
        print("编译eBPF程序...")
        bpf = BPF(text=bpf_code)
        print("✓ eBPF程序编译成功")
        
        # 加载函数
        fn = bpf.load_func("trace_connect", BPF.CGROUP_SOCK_ADDR)
        print("✓ eBPF函数加载成功")
        
        # 创建测试cgroup
        test_cgroup = "/sys/fs/cgroup/test_simple_ebpf"
        if not os.path.exists(test_cgroup):
            os.makedirs(test_cgroup)
        print(f"✓ 创建测试cgroup: {test_cgroup}")
        
        # 将当前进程加入cgroup
        with open(f"{test_cgroup}/cgroup.procs", "w") as f:
            f.write(str(os.getpid()))
        print(f"✓ 进程 {os.getpid()} 加入cgroup")
        
        # 附加eBPF程序
        bpf.attach_func(fn, test_cgroup, BPF.CGROUP_INET4_CONNECT)
        print("✓ eBPF程序附加到cgroup成功")
        
        # 测试网络连接
        print("\n测试网络连接...")
        test_hosts = [
            ("127.0.0.1", 22),   # SSH
            ("*******", 53),     # DNS
            ("*******", 53),     # DNS
        ]
        
        for host, port in test_hosts:
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(2)
                result = sock.connect_ex((host, port))
                sock.close()
                
                status = "成功" if result == 0 else "失败"
                print(f"  连接 {host}:{port} - {status}")
            except Exception as e:
                print(f"  连接 {host}:{port} - 异常: {e}")
        
        # 检查统计信息
        time.sleep(1)
        stats_table = bpf.get_table("stats")
        connection_count = stats_table[0].value if 0 in stats_table else 0
        print(f"\n✓ eBPF统计到 {connection_count} 次连接尝试")
        
        # 清理
        bpf.detach_func(fn, test_cgroup, BPF.CGROUP_INET4_CONNECT)
        print("✓ eBPF程序已分离")
        
        # 清理cgroup
        try:
            os.rmdir(test_cgroup)
            print("✓ 清理测试cgroup")
        except:
            print("⚠️  无法清理测试cgroup")
        
        return connection_count > 0
        
    except Exception as e:
        print(f"❌ 简单eBPF测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_network_policy_ebpf():
    """测试网络策略eBPF程序（真正的拦截）"""
    print("\n=== 测试网络策略eBPF程序 ===")
    
    try:
        from bcc import BPF
        
        # 网络策略eBPF程序 - 真正的拦截
        bpf_code = """
#include <uapi/linux/ptrace.h>
#include <linux/socket.h>

// 允许的端口列表
BPF_HASH(allowed_ports, u16, u8, 100);

// 统计信息
BPF_HASH(connection_stats, u32, u64, 10);

int network_policy_filter(struct bpf_sock_addr *ctx) {
    u16 port = bpf_ntohs(ctx->user_port);
    u32 ip = ctx->user_ip4;
    
    // 更新总连接数
    u32 total_key = 0;
    u64 *total = connection_stats.lookup(&total_key);
    if (total) {
        (*total)++;
    } else {
        u64 init_val = 1;
        connection_stats.update(&total_key, &init_val);
    }
    
    // 检查端口是否在允许列表中
    u8 *allowed = allowed_ports.lookup(&port);
    if (allowed && *allowed == 1) {
        // 更新允许连接数
        u32 allowed_key = 1;
        u64 *allowed_count = connection_stats.lookup(&allowed_key);
        if (allowed_count) {
            (*allowed_count)++;
        } else {
            u64 init_val = 1;
            connection_stats.update(&allowed_key, &init_val);
        }
        return 1; // 允许连接
    } else {
        // 更新拒绝连接数
        u32 denied_key = 2;
        u64 *denied_count = connection_stats.lookup(&denied_key);
        if (denied_count) {
            (*denied_count)++;
        } else {
            u64 init_val = 1;
            connection_stats.update(&denied_key, &init_val);
        }
        return 0; // 拒绝连接
    }
}
"""
        
        # 编译eBPF程序
        print("编译网络策略eBPF程序...")
        bpf = BPF(text=bpf_code)
        print("✓ 网络策略eBPF程序编译成功")
        
        # 配置允许的端口
        allowed_ports = bpf.get_table("allowed_ports")
        allowed_list = [53, 80, 443]  # DNS, HTTP, HTTPS
        for port in allowed_list:
            allowed_ports[port] = 1
        print(f"✓ 配置允许端口: {allowed_list}")
        
        # 加载和附加函数
        fn = bpf.load_func("network_policy_filter", BPF.CGROUP_SOCK_ADDR)
        
        test_cgroup = "/sys/fs/cgroup/test_network_policy"
        if not os.path.exists(test_cgroup):
            os.makedirs(test_cgroup)
        
        with open(f"{test_cgroup}/cgroup.procs", "w") as f:
            f.write(str(os.getpid()))
        
        bpf.attach_func(fn, test_cgroup, BPF.CGROUP_INET4_CONNECT)
        print("✓ 网络策略eBPF程序附加成功")
        
        # 测试拦截效果
        print("\n测试拦截效果...")
        test_cases = [
            ("*******", 53, True, "DNS查询 - 应该允许"),
            ("127.0.0.1", 80, True, "HTTP连接 - 应该允许"),
            ("127.0.0.1", 22, False, "SSH连接 - 应该拒绝"),
            ("127.0.0.1", 8080, False, "8080端口 - 应该拒绝"),
            ("127.0.0.1", 3306, False, "MySQL端口 - 应该拒绝"),
        ]
        
        results = []
        for host, port, should_succeed, description in test_cases:
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(1)
                start_time = time.time()
                result = sock.connect_ex((host, port))
                end_time = time.time()
                sock.close()
                
                success = (result == 0)
                duration = end_time - start_time
                expected = success == should_succeed
                
                status_icon = "✓" if expected else "✗"
                result_text = "成功" if success else "失败"
                expected_text = "成功" if should_succeed else "失败"
                
                print(f"  {status_icon} {description}")
                print(f"     连接 {host}:{port} - {result_text} (预期: {expected_text}) - {duration:.2f}s")
                
                results.append(expected)
                
            except Exception as e:
                print(f"  ✗ {description} - 异常: {e}")
                results.append(False)
        
        # 显示统计信息
        time.sleep(1)
        connection_stats = bpf.get_table("connection_stats")
        
        total = connection_stats[0].value if 0 in connection_stats else 0
        allowed = connection_stats[1].value if 1 in connection_stats else 0
        denied = connection_stats[2].value if 2 in connection_stats else 0
        
        print(f"\n=== eBPF统计信息 ===")
        print(f"总连接尝试: {total}")
        print(f"允许连接: {allowed}")
        print(f"拒绝连接: {denied}")
        
        # 清理
        bpf.detach_func(fn, test_cgroup, BPF.CGROUP_INET4_CONNECT)
        print("✓ 网络策略eBPF程序已分离")
        
        try:
            os.rmdir(test_cgroup)
            print("✓ 清理测试cgroup")
        except:
            print("⚠️  无法清理测试cgroup")
        
        # 评估结果
        passed = sum(results)
        total_tests = len(results)
        success_rate = passed / total_tests if total_tests > 0 else 0
        
        print(f"\n测试结果: {passed}/{total_tests} 通过 ({success_rate:.1%})")
        
        return success_rate >= 0.8 and total > 0  # 80%通过率且有连接统计

    except Exception as e:
        print(f"❌ 网络策略eBPF测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("eBPF网络拦截功能测试")
    print("=" * 50)

    # 检查前提条件
    if not check_prerequisites():
        print("\n❌ 前提条件检查失败")
        return 1

    success = True

    # 测试简单eBPF程序
    if not test_simple_ebpf():
        print("❌ 简单eBPF测试失败")
        success = False

    # 测试网络策略eBPF程序
    if not test_network_policy_ebpf():
        print("❌ 网络策略eBPF测试失败")
        success = False

    # 总结
    if success:
        print("\n🎉 eBPF网络拦截功能测试成功！")
        print("✓ eBPF程序能够成功编译和加载")
        print("✓ eBPF程序能够附加到cgroup")
        print("✓ eBPF程序能够真正拦截网络连接")
        print("✓ 统计功能正常工作")
    else:
        print("\n❌ eBPF网络拦截功能测试失败")
        print("请检查:")
        print("- 是否有root权限")
        print("- BCC是否正确安装")
        print("- 内核是否支持eBPF")
        print("- cgroup是否正确配置")

    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
