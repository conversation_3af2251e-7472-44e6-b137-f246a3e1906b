[build-system]
requires = ["setuptools>=45", "wheel", "setuptools_scm[toml]>=6.2"]
build-backend = "setuptools.build_meta"

[project]
name = "bpf-network-policy"
version = "1.0.0"
description = "eBPF-based Network Policy Control System"
readme = "docs/README.md"
license = {text = "MIT"}
authors = [
    {name = "BPF Policy Team", email = "<EMAIL>"}
]
maintainers = [
    {name = "BPF Policy Team", email = "<EMAIL>"}
]
keywords = ["bpf", "ebpf", "network", "policy", "security", "firewall", "linux", "kernel"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: System Administrators",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: POSIX :: Linux",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.7",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: C",
    "Topic :: System :: Networking :: Firewalls",
    "Topic :: System :: Systems Administration",
    "Topic :: Security",
]
requires-python = ">=3.7"
dependencies = [
    "PyYAML>=6.0,<7.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "pytest-mock>=3.10.0",
    "black>=23.0.0",
    "flake8>=6.0.0",
    "mypy>=1.0.0",
    "isort>=5.12.0",
]
docs = [
    "sphinx>=6.0.0",
    "sphinx-rtd-theme>=1.2.0",
]
monitoring = [
    "structlog>=23.0.0",
    "colorlog>=6.7.0",
]

[project.urls]
Homepage = "https://github.com/example/bpf-network-policy"
Documentation = "https://bpf-network-policy.readthedocs.io/"
Repository = "https://github.com/example/bpf-network-policy"
"Bug Reports" = "https://github.com/example/bpf-network-policy/issues"

[project.scripts]
bpf-policy-control = "bpf_network_policy.core.policy_control:main"
bpf-legacy-control = "bpf_network_policy.core.user_control:main"
bpf-policy-test = "tests.integration.test_yaml_simple:main"
bpf-intercept-test = "tests.integration.test_basic_intercept:main"

[tool.setuptools]
package-dir = {"" = "src"}

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-data]
"bpf_network_policy.bpf" = ["*.c", "*.h"]
"config" = ["*.yaml", "*.yml"]
"config.examples" = ["*.yaml", "*.yml"]

# Black configuration
[tool.black]
line-length = 88
target-version = ['py37', 'py38', 'py39', 'py310', 'py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

# isort configuration
[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["bpf_network_policy"]
known_third_party = ["bcc", "yaml", "pytest"]

# pytest configuration
[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--verbose",
    "--tb=short",
    "--strict-markers",
    "--disable-warnings"
]
markers = [
    "unit: Unit tests",
    "integration: Integration tests", 
    "requires_root: Tests requiring root privileges",
    "requires_bcc: Tests requiring BCC framework",
    "slow: Slow running tests"
]

# Coverage configuration
[tool.coverage.run]
source = ["src/bpf_network_policy"]
omit = [
    "*/tests/*",
    "*/test_*",
    "setup.py"
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "raise AssertionError",
    "raise NotImplementedError",
    "if __name__ == .__main__.:"
]

# MyPy configuration
[tool.mypy]
python_version = "3.7"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = "bcc.*"
ignore_missing_imports = true
