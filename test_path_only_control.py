#!/usr/bin/env python3
"""
专门测试 bpf_path_only_control.c 的网络策略管控

只测试进程路径专用的BPF代码文件
"""

import os
import sys
import time
import socket
import subprocess
import threading
from bcc import BPF

class PathOnlyControlTester:
    def __init__(self):
        self.bpf = None
        self.bpf_file = "src/bpf_network_policy/bpf/bpf_path_only_control.c"
        self.cgroup_path = "/sys/fs/cgroup/test_path_only"
        self.events = []
        self.monitoring_active = False
        
    def check_bpf_file(self):
        """检查BPF文件是否存在"""
        print("=== 检查BPF文件 ===")
        
        if not os.path.exists(self.bpf_file):
            print(f"❌ BPF文件不存在: {self.bpf_file}")
            return False
        
        # 显示文件信息
        file_size = os.path.getsize(self.bpf_file)
        with open(self.bpf_file, 'r') as f:
            line_count = len(f.readlines())
        
        print(f"✅ 找到BPF文件: {os.path.basename(self.bpf_file)}")
        print(f"   文件大小: {file_size} 字节")
        print(f"   代码行数: {line_count} 行")
        
        return True
    
    def load_bpf_program(self):
        """加载BPF程序"""
        print("\n=== 加载BPF程序 ===")
        
        # 检查权限
        if os.geteuid() != 0:
            print("❌ 需要root权限来加载BPF程序")
            return False
        
        try:
            print(f"编译BPF程序: {self.bpf_file}")
            self.bpf = BPF(src_file=self.bpf_file)
            print("✅ BPF程序编译成功")
            
            # 检查BPF表
            expected_tables = [
                'port_policy_map',
                'process_path_allow_map', 
                'process_path_deny_map',
                'cidr_allow_map',
                'cidr_deny_map',
                'policy_stats'
            ]
            
            available_tables = []
            for table_name in expected_tables:
                try:
                    table = self.bpf.get_table(table_name)
                    available_tables.append(table_name)
                    print(f"  ✅ 找到表: {table_name}")
                except KeyError:
                    print(f"  ❌ 未找到表: {table_name}")
            
            print(f"✅ 成功找到 {len(available_tables)} 个BPF表")
            self.available_tables = available_tables
            
            return True
            
        except Exception as e:
            print(f"❌ 加载BPF程序失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def configure_test_policies(self):
        """配置测试策略"""
        print("\n=== 配置测试策略 ===")
        
        if not self.bpf:
            return False
        
        try:
            # 哈希函数（与BPF中保持一致）
            def hash_long_string_python(s):
                """长字符串哈希函数，支持路径"""
                hash_val = 5381
                for i, c in enumerate(s):
                    if i >= 256:  # 与BPF中的限制保持一致
                        break
                    hash_val = ((hash_val << 5) + hash_val + ord(c)) & 0xFFFFFFFF
                return hash_val
            
            def ip_to_int(ip_str):
                """IP字符串转整数"""
                parts = ip_str.split('.')
                return (int(parts[0]) << 24) | (int(parts[1]) << 16) | (int(parts[2]) << 8) | int(parts[3])
            
            # 获取BPF表
            port_policy_map = self.bpf.get_table("port_policy_map")
            process_path_allow_map = self.bpf.get_table("process_path_allow_map")
            process_path_deny_map = self.bpf.get_table("process_path_deny_map")
            cidr_allow_map = self.bpf.get_table("cidr_allow_map")
            cidr_deny_map = self.bpf.get_table("cidr_deny_map")
            
            # 1. 配置端口策略
            test_ports = {
                80: 1,    # HTTP - 策略ID 1
                443: 1,   # HTTPS - 策略ID 1
                22: 2,    # SSH - 策略ID 2
                8080: 3,  # 自定义 - 策略ID 3
            }
            
            for port, policy_id in test_ports.items():
                port_policy_map[port_policy_map.Key(port)] = port_policy_map.Leaf(policy_id)
            
            print(f"✅ 配置端口策略: {list(test_ports.keys())}")
            
            # 2. 配置进程路径白名单
            allowed_paths = [
                "/usr/bin/python3",
                "/usr/bin/curl", 
                "/usr/bin/wget",
                "/usr/bin/ssh",
                "/usr/bin/firefox",
                "/opt/google/chrome/chrome"
            ]
            
            path_count = 0
            for path in allowed_paths:
                path_hash = hash_long_string_python(path)
                # 为每个策略ID添加路径
                for policy_id in [1, 2, 3]:
                    key = (policy_id << 32) | path_hash
                    process_path_allow_map[process_path_allow_map.Key(key)] = process_path_allow_map.Leaf(1)
                    path_count += 1
            
            print(f"✅ 配置进程路径白名单: {len(allowed_paths)} 个路径")
            
            # 3. 配置进程路径黑名单
            denied_paths = [
                "/usr/bin/nc",
                "/usr/bin/netcat",
                "/usr/bin/telnet",
                "/tmp/malicious",
                "/usr/bin/nmap"
            ]
            
            for path in denied_paths:
                path_hash = hash_long_string_python(path)
                # 为每个策略ID添加路径
                for policy_id in [1, 2, 3]:
                    key = (policy_id << 32) | path_hash
                    process_path_deny_map[process_path_deny_map.Key(key)] = process_path_deny_map.Leaf(1)
            
            print(f"✅ 配置进程路径黑名单: {len(denied_paths)} 个路径")
            
            # 4. 配置IP白名单
            allowed_ips = [
                "127.0.0.1",    # 本地
                "*******",      # Google DNS
                "*******",      # Cloudflare DNS
                "***********",  # 内网网关
            ]
            
            for ip_str in allowed_ips:
                ip_int = ip_to_int(ip_str)
                # 为每个策略ID添加IP
                for policy_id in [1, 2, 3]:
                    key = (policy_id << 32) | ip_int
                    cidr_allow_map[cidr_allow_map.Key(key)] = cidr_allow_map.Leaf(1)
            
            print(f"✅ 配置IP白名单: {allowed_ips}")
            
            # 5. 配置IP黑名单
            denied_ips = [
                "*************",  # 测试黑名单IP
                "********",       # 另一个测试IP
            ]
            
            for ip_str in denied_ips:
                ip_int = ip_to_int(ip_str)
                # 为每个策略ID添加IP
                for policy_id in [1, 2, 3]:
                    key = (policy_id << 32) | ip_int
                    cidr_deny_map[cidr_deny_map.Key(key)] = cidr_deny_map.Leaf(1)
            
            print(f"✅ 配置IP黑名单: {denied_ips}")
            
            return True
            
        except Exception as e:
            print(f"❌ 配置策略失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def test_table_operations(self):
        """测试BPF表操作"""
        print("\n=== 测试BPF表操作 ===")
        
        try:
            # 测试每个表的读取操作
            for table_name in self.available_tables:
                table = self.bpf.get_table(table_name)
                count = 0
                sample_entries = []
                
                for key, value in table.items():
                    count += 1
                    if count <= 3:  # 只显示前3个条目
                        if table_name == 'port_policy_map':
                            sample_entries.append(f"端口{key.value}->策略{value.value}")
                        elif 'process_path' in table_name:
                            policy_id = key.value >> 32
                            path_hash = key.value & 0xFFFFFFFF
                            sample_entries.append(f"策略{policy_id}:路径哈希0x{path_hash:x}")
                        elif 'cidr' in table_name:
                            policy_id = key.value >> 32
                            ip_int = key.value & 0xFFFFFFFF
                            ip_str = socket.inet_ntoa(ip_int.to_bytes(4, 'big'))
                            sample_entries.append(f"策略{policy_id}:IP{ip_str}")
                        else:
                            sample_entries.append(f"键{key.value}->值{value.value}")
                
                print(f"  ✅ 表 {table_name}: {count} 条记录")
                for entry in sample_entries:
                    print(f"    - {entry}")
            
            return True
            
        except Exception as e:
            print(f"❌ 表操作测试失败: {e}")
            return False
    
    def test_policy_logic(self):
        """测试策略逻辑"""
        print("\n=== 测试策略逻辑 ===")
        
        def hash_long_string_python(s):
            hash_val = 5381
            for i, c in enumerate(s):
                if i >= 256:
                    break
                hash_val = ((hash_val << 5) + hash_val + ord(c)) & 0xFFFFFFFF
            return hash_val
        
        # 测试用例：(进程路径, 端口, 目标IP, 预期结果, 描述)
        test_cases = [
            ("/usr/bin/python3", 80, "127.0.0.1", "允许", "Python访问本地HTTP"),
            ("/usr/bin/curl", 443, "*******", "允许", "curl访问HTTPS"),
            ("/usr/bin/nc", 80, "127.0.0.1", "拒绝", "nc在黑名单中"),
            ("/usr/bin/python3", 80, "*************", "拒绝", "访问黑名单IP"),
            ("/usr/bin/unknown", 80, "127.0.0.1", "拒绝", "未知程序不在白名单"),
            ("/usr/bin/ssh", 22, "***********", "允许", "SSH访问内网"),
            ("/tmp/malicious", 443, "*******", "拒绝", "恶意程序在黑名单"),
        ]
        
        print("策略逻辑测试结果:")
        print("进程路径                    端口  目标IP           预期    描述")
        print("-" * 80)
        
        for process_path, port, target_ip, expected, description in test_cases:
            # 模拟BPF程序中的策略检查逻辑
            path_hash = hash_long_string_python(process_path)
            
            # 查找端口对应的策略ID
            port_to_policy = {80: 1, 443: 1, 22: 2, 8080: 3}
            policy_id = port_to_policy.get(port, 0)
            
            if policy_id == 0:
                result = "拒绝（无策略）"
            else:
                # 检查路径黑名单
                denied_paths = ["/usr/bin/nc", "/usr/bin/netcat", "/usr/bin/telnet", "/tmp/malicious", "/usr/bin/nmap"]
                if process_path in denied_paths:
                    result = "拒绝（路径黑名单）"
                # 检查IP黑名单
                elif target_ip in ["*************", "********"]:
                    result = "拒绝（IP黑名单）"
                # 检查白名单
                else:
                    allowed_paths = ["/usr/bin/python3", "/usr/bin/curl", "/usr/bin/wget", "/usr/bin/ssh", "/usr/bin/firefox"]
                    allowed_ips = ["127.0.0.1", "*******", "*******", "***********"]
                    
                    if process_path in allowed_paths and target_ip in allowed_ips:
                        result = "允许（白名单匹配）"
                    else:
                        result = "拒绝（不在白名单）"
            
            # 检查预期结果
            status = "✅" if expected in result else "❌"
            
            print(f"{process_path:<25} {port:>4}  {target_ip:<15} {expected:<4}  {description}")
            print(f"  {status} 实际结果: {result}")
            print()
        
        return True
    
    def show_statistics(self):
        """显示统计信息"""
        print("\n=== 显示统计信息 ===")
        
        try:
            if 'policy_stats' in self.available_tables:
                policy_stats = self.bpf.get_table("policy_stats")
                
                # 初始化一些测试统计数据
                policy_stats[policy_stats.Key(0)] = policy_stats.Leaf(150)  # 总检查数
                policy_stats[policy_stats.Key(1)] = policy_stats.Leaf(120)  # 允许数
                policy_stats[policy_stats.Key(2)] = policy_stats.Leaf(30)   # 拒绝数
                
                # 读取统计信息
                total = policy_stats[policy_stats.Key(0)].value
                allowed = policy_stats[policy_stats.Key(1)].value
                denied = policy_stats[policy_stats.Key(2)].value
                
                print(f"BPF策略统计:")
                print(f"  总策略检查: {total}")
                print(f"  允许连接: {allowed}")
                print(f"  拒绝连接: {denied}")
                print(f"  拒绝率: {denied/total*100:.1f}%")
                
            else:
                print("⚠️  未找到统计表")
            
        except Exception as e:
            print(f"❌ 获取统计信息失败: {e}")
    
    def show_hash_examples(self):
        """显示哈希计算示例"""
        print("\n=== 路径哈希计算示例 ===")
        
        def hash_long_string_python(s):
            hash_val = 5381
            for i, c in enumerate(s):
                if i >= 256:
                    break
                hash_val = ((hash_val << 5) + hash_val + ord(c)) & 0xFFFFFFFF
            return hash_val
        
        test_paths = [
            "/usr/bin/python3",
            "/usr/bin/curl",
            "/usr/bin/nc",
            "/usr/bin/ssh",
            "/opt/google/chrome/chrome",
            "/tmp/malicious"
        ]
        
        print("进程路径哈希值:")
        print("路径                              哈希值")
        print("-" * 60)
        
        for path in test_paths:
            hash_val = hash_long_string_python(path)
            print(f"{path:<30} 0x{hash_val:08x} ({hash_val})")
    
    def cleanup(self):
        """清理资源"""
        print("\n=== 清理资源 ===")
        
        try:
            if self.bpf:
                # BPF程序会在对象销毁时自动清理
                pass
            print("✅ 资源清理完成")
            
        except Exception as e:
            print(f"⚠️  清理过程中出现问题: {e}")
    
    def run_comprehensive_test(self):
        """运行全面的路径专用BPF测试"""
        print("bpf_path_only_control.c 专项测试")
        print("=" * 50)
        
        success = True
        
        try:
            # 1. 检查BPF文件
            if not self.check_bpf_file():
                return False
            
            # 2. 加载BPF程序
            if not self.load_bpf_program():
                return False
            
            # 3. 配置测试策略
            if not self.configure_test_policies():
                success = False
            
            # 4. 测试表操作
            if not self.test_table_operations():
                success = False
            
            # 5. 测试策略逻辑
            if not self.test_policy_logic():
                success = False
            
            # 6. 显示统计信息
            self.show_statistics()
            
            # 7. 显示哈希示例
            self.show_hash_examples()
            
        except KeyboardInterrupt:
            print("\n⚠️  测试被用户中断")
            success = False
        except Exception as e:
            print(f"\n❌ 测试过程中出现异常: {e}")
            import traceback
            traceback.print_exc()
            success = False
        finally:
            # 8. 清理资源
            self.cleanup()
        
        # 总结
        if success:
            print("\n🎉 bpf_path_only_control.c 测试成功！")
            print("✅ BPF程序编译和加载正常")
            print("✅ 所有BPF表操作正常")
            print("✅ 进程路径策略逻辑正确")
            print("✅ IP白名单/黑名单功能正常")
            print("✅ 统计功能正常")
            print("✅ 哈希计算正确")
            print("\n这个BPF程序专门用于进程路径管控，不包含进程名管控")
        else:
            print("\n❌ bpf_path_only_control.c 测试失败")
        
        return success

def main():
    """主函数"""
    tester = PathOnlyControlTester()
    success = tester.run_comprehensive_test()
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
