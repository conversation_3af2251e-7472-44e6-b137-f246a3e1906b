# BPF Network Policy Control System Requirements
# 
# This file specifies the Python dependencies required for the
# BPF network policy control system.

# Core BPF functionality
# Note: BCC (BPF Compiler Collection) must be installed system-wide
# as it requires kernel headers and LLVM/Clang toolchain
# Installation: sudo apt-get install bpfcc-tools python3-bpfcc

# YAML configuration parsing
PyYAML>=6.0,<7.0

# Network utilities (usually included in Python standard library)
# ipaddress - included in Python 3.3+
# socket - included in Python standard library
# struct - included in Python standard library

# Development and testing dependencies
pytest>=7.0.0,<8.0.0
pytest-cov>=4.0.0,<5.0.0
pytest-mock>=3.10.0,<4.0.0

# Code quality tools
black>=23.0.0,<24.0.0
flake8>=6.0.0,<7.0.0
mypy>=1.0.0,<2.0.0
isort>=5.12.0,<6.0.0

# Documentation
sphinx>=6.0.0,<7.0.0
sphinx-rtd-theme>=1.2.0,<2.0.0

# Optional: Enhanced logging and monitoring
structlog>=23.0.0,<24.0.0
colorlog>=6.7.0,<7.0.0

# Note: System-level dependencies that must be installed separately:
# - Linux kernel with eBPF support (4.1+)
# - BCC (BPF Compiler Collection)
# - LLVM/Clang compiler toolchain
# - Kernel headers
#
# Ubuntu/Debian installation:
# sudo apt-get update
# sudo apt-get install -y \
#     bpfcc-tools \
#     python3-bpfcc \
#     linux-headers-$(uname -r) \
#     build-essential \
#     clang \
#     llvm
#
# CentOS/RHEL installation:
# sudo yum install -y \
#     bcc-tools \
#     python3-bcc \
#     kernel-devel \
#     clang \
#     llvm
