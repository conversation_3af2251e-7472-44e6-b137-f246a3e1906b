# BPF Network Policy Control System - Deployment Guide

## 🚀 Production Deployment Guide

This guide provides comprehensive instructions for deploying the BPF Network Policy Control System in production environments.

## 📋 Prerequisites

### System Requirements

- **Operating System**: Linux with kernel 4.1+ (Ubuntu 18.04+, CentOS 7+, RHEL 7+)
- **Architecture**: x86_64 (amd64)
- **Memory**: Minimum 2GB RAM, Recommended 4GB+
- **Storage**: Minimum 1GB free space
- **Network**: Outbound internet access for package installation

### Required Privileges

- **Root access** for BPF program loading
- **Sudo privileges** for system package installation
- **Network administration** capabilities

## 🛠️ Installation Steps

### Step 1: System Preparation

#### Ubuntu/Debian Systems
```bash
# Update system packages
sudo apt-get update && sudo apt-get upgrade -y

# Install system dependencies
sudo apt-get install -y \
    bpfcc-tools \
    python3-bpfcc \
    linux-headers-$(uname -r) \
    build-essential \
    clang \
    llvm \
    python3-pip \
    python3-venv \
    git

# Verify BCC installation
python3 -c "from bcc import BPF; print('BCC installed successfully')"
```

#### CentOS/RHEL Systems
```bash
# Enable EPEL repository
sudo yum install -y epel-release

# Install system dependencies
sudo yum install -y \
    bcc-tools \
    python3-bcc \
    kernel-devel \
    gcc \
    clang \
    llvm \
    python3-pip \
    git

# Verify BCC installation
python3 -c "from bcc import BPF; print('BCC installed successfully')"
```

### Step 2: Application Installation

#### Option A: Automated Installation
```bash
# Clone the repository
git clone <repository-url> /opt/bpf-network-policy
cd /opt/bpf-network-policy

# Run automated installation script
chmod +x scripts/install.sh
./scripts/install.sh
```

#### Option B: Manual Installation
```bash
# Clone the repository
git clone <repository-url> /opt/bpf-network-policy
cd /opt/bpf-network-policy

# Create virtual environment
python3 -m venv venv
source venv/bin/activate

# Install Python dependencies
pip install -r requirements.txt

# Install the package
pip install -e .
```

#### Option B: Package Installation
```bash
# Install from package
pip install bpf-network-policy

# Or install from wheel
pip install bpf_network_policy-1.0.0-py3-none-any.whl
```

### Step 3: Configuration

#### Create Configuration Directory
```bash
sudo mkdir -p /etc/bpf-network-policy
sudo mkdir -p /var/log/bpf-network-policy
sudo mkdir -p /var/lib/bpf-network-policy
```

#### Copy Configuration Files
```bash
# Copy default configuration
sudo cp config/policy_config.yaml /etc/bpf-network-policy/
sudo cp -r config/examples /etc/bpf-network-policy/
sudo cp src/bpf_network_policy/bpf/*.c /etc/bpf-network-policy/

# Set appropriate permissions
sudo chown -R root:root /etc/bpf-network-policy
sudo chmod 644 /etc/bpf-network-policy/*.yaml
sudo chmod -R 644 /etc/bpf-network-policy/examples/
sudo chmod 644 /etc/bpf-network-policy/*.c
```

#### Customize Policy Configuration
```bash
# Edit the policy configuration
sudo nano /etc/bpf-network-policy/policy_config.yaml

# Validate configuration
python3 -c "
from bpf_utils import policy_utils
config = policy_utils.load_yaml_config('/etc/bpf-network-policy/policy_config.yaml')
print('Configuration is valid')
"
```

## 🔧 Service Configuration

### Create Systemd Service

#### Main Policy Service
```bash
sudo tee /etc/systemd/system/bpf-policy-control.service > /dev/null << 'EOF'
[Unit]
Description=BPF Network Policy Control Service
After=network.target
Wants=network.target

[Service]
Type=simple
User=root
Group=root
WorkingDirectory=/opt/bpf-network-policy
ExecStart=/opt/bpf-network-policy/venv/bin/python3 -m bpf_network_policy.core.policy_control /etc/bpf-network-policy/policy_config.yaml
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=bpf-policy-control

# Security settings
NoNewPrivileges=false
PrivateTmp=true
ProtectSystem=strict
ReadWritePaths=/var/log/bpf-network-policy /var/lib/bpf-network-policy

[Install]
WantedBy=multi-user.target
EOF
```

#### Enable and Start Service
```bash
# Reload systemd configuration
sudo systemctl daemon-reload

# Enable service to start on boot
sudo systemctl enable bpf-policy-control

# Start the service
sudo systemctl start bpf-policy-control

# Check service status
sudo systemctl status bpf-policy-control
```

### Configure Logging

#### Rsyslog Configuration
```bash
sudo tee /etc/rsyslog.d/50-bpf-policy.conf > /dev/null << 'EOF'
# BPF Network Policy Control logging
:programname, isequal, "bpf-policy-control" /var/log/bpf-network-policy/policy-control.log
& stop
EOF

# Restart rsyslog
sudo systemctl restart rsyslog
```

#### Log Rotation
```bash
sudo tee /etc/logrotate.d/bpf-policy-control > /dev/null << 'EOF'
/var/log/bpf-network-policy/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 root root
    postrotate
        systemctl reload bpf-policy-control
    endscript
}
EOF
```

## 📊 Monitoring & Alerting

### Health Check Script
```bash
sudo tee /usr/local/bin/bpf-policy-healthcheck > /dev/null << 'EOF'
#!/bin/bash
# BPF Policy Control Health Check

SERVICE_NAME="bpf-policy-control"
LOG_FILE="/var/log/bpf-network-policy/healthcheck.log"

# Check if service is running
if systemctl is-active --quiet $SERVICE_NAME; then
    echo "$(date): Service $SERVICE_NAME is running" >> $LOG_FILE
    exit 0
else
    echo "$(date): Service $SERVICE_NAME is not running" >> $LOG_FILE
    # Attempt to restart
    systemctl restart $SERVICE_NAME
    sleep 5
    if systemctl is-active --quiet $SERVICE_NAME; then
        echo "$(date): Service $SERVICE_NAME restarted successfully" >> $LOG_FILE
        exit 0
    else
        echo "$(date): Failed to restart service $SERVICE_NAME" >> $LOG_FILE
        exit 1
    fi
fi
EOF

sudo chmod +x /usr/local/bin/bpf-policy-healthcheck
```

### Cron Job for Health Checks
```bash
# Add health check to crontab
echo "*/5 * * * * root /usr/local/bin/bpf-policy-healthcheck" | sudo tee -a /etc/crontab
```

## 🔒 Security Configuration

### Firewall Configuration
```bash
# Allow necessary ports (adjust as needed)
sudo ufw allow 22/tcp    # SSH
sudo ufw allow 53/tcp    # DNS
sudo ufw allow 53/udp    # DNS
sudo ufw allow 80/tcp    # HTTP
sudo ufw allow 443/tcp   # HTTPS

# Enable firewall
sudo ufw --force enable
```

### SELinux Configuration (CentOS/RHEL)
```bash
# Create SELinux policy for BPF programs
sudo setsebool -P domain_can_mmap_files 1
sudo setsebool -P domain_kernel_load_modules 1

# Set appropriate SELinux contexts
sudo semanage fcontext -a -t bin_t "/opt/bpf-network-policy/venv/bin/python3"
sudo restorecon -R /opt/bpf-network-policy
```

## 🧪 Testing & Validation

### Deployment Validation
```bash
# Test policy loading
sudo python3 /opt/bpf-network-policy/test_yaml_simple.py

# Test BPF program compilation
sudo python3 -c "
from bcc import BPF
bpf = BPF(src_file='/etc/bpf-network-policy/bpf_policy_control.c')
print('BPF program compiled successfully')
"

# Test service functionality
sudo systemctl status bpf-policy-control
sudo journalctl -u bpf-policy-control -n 20
```

### Performance Testing
```bash
# Run performance tests
sudo python3 /opt/bpf-network-policy/test_basic_intercept.py

# Monitor system resources
top -p $(pgrep -f bpf-policy-control)
```

## 🔄 Maintenance Procedures

### Regular Maintenance Tasks

#### Daily
- Check service status
- Review log files for errors
- Monitor system resources

#### Weekly
- Update system packages
- Review policy effectiveness
- Check for security updates

#### Monthly
- Full system backup
- Performance optimization review
- Security audit

### Backup Procedures
```bash
# Create backup script
sudo tee /usr/local/bin/bpf-policy-backup > /dev/null << 'EOF'
#!/bin/bash
BACKUP_DIR="/var/backups/bpf-network-policy"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR

# Backup configuration
tar -czf $BACKUP_DIR/config_$DATE.tar.gz /etc/bpf-network-policy/

# Backup logs
tar -czf $BACKUP_DIR/logs_$DATE.tar.gz /var/log/bpf-network-policy/

# Keep only last 30 days of backups
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete

echo "Backup completed: $DATE"
EOF

sudo chmod +x /usr/local/bin/bpf-policy-backup

# Schedule daily backups
echo "0 2 * * * root /usr/local/bin/bpf-policy-backup" | sudo tee -a /etc/crontab
```

## 🚨 Troubleshooting

### Common Issues

#### Service Won't Start
```bash
# Check service logs
sudo journalctl -u bpf-policy-control -f

# Check BPF program compilation
sudo python3 -c "from bcc import BPF; BPF(src_file='/etc/bpf-network-policy/bpf_policy_control.c')"

# Verify permissions
ls -la /etc/bpf-network-policy/
```

#### Policy Not Working
```bash
# Validate configuration
python3 /opt/bpf-network-policy/test_yaml_simple.py

# Check BPF maps
sudo python3 -c "
from bcc import BPF
bpf = BPF(src_file='/etc/bpf-network-policy/bpf_policy_control.c')
print('Port policy map:', dict(bpf.get_table('port_policy_map').items()))
"
```

#### Performance Issues
```bash
# Monitor system resources
htop
iotop
nethogs

# Check BPF program efficiency
sudo bpftool prog list
sudo bpftool map list
```

## 📞 Support & Documentation

### Log Locations
- **Service logs**: `/var/log/bpf-network-policy/`
- **System logs**: `journalctl -u bpf-policy-control`
- **Health check logs**: `/var/log/bpf-network-policy/healthcheck.log`

### Configuration Files
- **Main config**: `/etc/bpf-network-policy/policy_config.yaml`
- **BPF programs**: `/etc/bpf-network-policy/*.c`
- **Service config**: `/etc/systemd/system/bpf-policy-control.service`

### Useful Commands
```bash
# Service management
sudo systemctl {start|stop|restart|status} bpf-policy-control

# Configuration validation
python3 test_yaml_simple.py

# Real-time monitoring
sudo journalctl -u bpf-policy-control -f

# Performance monitoring
sudo bpftool prog show
sudo bpftool map show
```

This deployment guide provides a comprehensive foundation for production deployment. Adjust configurations based on your specific environment and security requirements.
