# 进程绝对路径控制

本文档介绍如何在BPF网络策略系统中使用进程绝对路径进行更精确的进程控制。

## 概述

传统的进程控制通常基于进程名称（如 `curl`、`ssh`），但这种方法存在安全风险：

1. **进程名欺骗**：恶意程序可以使用合法进程的名称
2. **路径混淆**：同名程序可能来自不同位置
3. **精度不足**：无法区分不同版本或安装位置的程序

进程绝对路径控制通过检查进程的完整可执行文件路径来解决这些问题。

## 工作原理

### 1. 路径获取

BPF程序通过以下方式获取进程路径：

```c
// 方法1：从用户空间缓存获取（推荐）
u64 *cached_path_hash = pid_path_cache.lookup(&pid);

// 方法2：直接从内核获取（某些内核版本可能不支持）
char proc_path[256];
int path_ret = get_process_path(proc_path, sizeof(proc_path));
```

### 2. 路径哈希

为了在BPF map中高效存储和查找，路径被转换为哈希值：

```c
// 支持长路径的哈希函数
static inline u32 hash_long_string(char *str, int max_len) {
    u32 hash = 5381;
    for (int i = 0; i < max_len && str[i] != 0; i++) {
        hash = ((hash << 5) + hash) + str[i];
    }
    return hash;
}
```

### 3. 策略检查

BPF程序按以下顺序检查策略：

1. **路径黑名单检查**：如果进程路径在黑名单中，立即拒绝
2. **路径白名单检查**：检查进程路径是否在白名单中
3. **名称检查**：作为后备，检查进程名称策略
4. **最终决策**：路径白名单 OR 名称白名单 AND IP白名单

## 配置格式

### YAML配置示例

```yaml
ports:
  - ports: ["80", "443"]
    process:
      # 传统进程名控制
      process_allow_list:
        - "curl"
        - "wget"
      
      # 进程路径控制（新增）
      process_path_allow_list:
        - "/usr/bin/curl"
        - "/usr/bin/wget"
        - "/opt/google/chrome/chrome"
        - "/snap/firefox/current/usr/lib/firefox/firefox"
      
      process_path_deny_list:
        - "/tmp/malicious_tool"
        - "/var/tmp/suspicious_binary"
        - "/home/<USER>/Downloads/untrusted_app"
```

### 配置字段说明

| 字段 | 类型 | 说明 |
|------|------|------|
| `process_path_allow_list` | 列表 | 允许的进程绝对路径 |
| `process_path_deny_list` | 列表 | 禁止的进程绝对路径 |

## 使用示例

### 1. Web浏览器控制

```yaml
- ports: ["80", "443"]
  process:
    process_path_allow_list:
      - "/usr/bin/firefox"
      - "/usr/bin/google-chrome"
      - "/opt/google/chrome/chrome"
      - "/snap/firefox/current/usr/lib/firefox/firefox"
    process_path_deny_list:
      - "/tmp/fake_browser"
      - "/home/<USER>/Downloads/browser"
```

### 2. SSH客户端控制

```yaml
- ports: ["22"]
  process:
    process_path_allow_list:
      - "/usr/bin/ssh"
      - "/usr/bin/scp"
      - "/usr/bin/sftp"
    process_path_deny_list:
      - "/tmp/ssh"
      - "/var/tmp/backdoor_ssh"
```

### 3. 数据库访问控制

```yaml
- ports: ["3306", "5432"]
  process:
    process_path_allow_list:
      - "/usr/bin/mysql"
      - "/usr/bin/psql"
      - "/usr/sbin/mysqld"
      - "/usr/lib/postgresql/*/bin/postgres"
    process_path_deny_list:
      - "/tmp/mysql"
      - "/home/<USER>/mysql_client"
```

## 安全优势

### 1. 防止进程名欺骗

**攻击场景**：
```bash
# 攻击者创建恶意程序
cp /tmp/malware /tmp/curl
chmod +x /tmp/curl

# 传统名称检查会被绕过
/tmp/curl http://malicious-site.com  # 进程名是"curl"，会被允许
```

**路径检查防护**：
```yaml
process_path_allow_list:
  - "/usr/bin/curl"  # 只允许系统curl
# /tmp/curl 会被拒绝
```

### 2. 防止路径混淆

**问题**：不同位置的同名程序可能有不同的安全级别
```bash
/usr/bin/python3      # 系统Python，可信
/home/<USER>/python3    # 用户Python，可能不安全
/tmp/python3          # 临时Python，高度可疑
```

**解决方案**：
```yaml
process_path_allow_list:
  - "/usr/bin/python3"
  - "/usr/local/bin/python3"
process_path_deny_list:
  - "/tmp/python3"
  - "/var/tmp/python3"
```

## 实现细节

### 1. BPF Map结构

```c
// 进程路径白名单 - key: policy_id << 32 | process_path_hash, value: 1
BPF_HASH(process_path_allow_map, u64, u8, 2048);

// 进程路径黑名单 - key: policy_id << 32 | process_path_hash, value: 1
BPF_HASH(process_path_deny_map, u64, u8, 2048);

// PID到路径哈希的缓存
BPF_HASH(pid_path_cache, u32, u64, 1024);
```

### 2. 用户空间支持

Python工具类提供路径管理功能：

```python
from bpf_network_policy.utils import ProcessUtils, StringUtils

# 获取进程路径
path = ProcessUtils.get_process_path(pid)

# 计算路径哈希
path_hash = StringUtils.hash_long_string(path)

# 验证路径格式
is_valid = ProcessUtils.validate_process_path(path)
```

## 性能考虑

### 1. 哈希冲突

- 使用djb2哈希算法，冲突概率较低
- 长路径增加了哈希的唯一性
- 建议监控哈希冲突情况

### 2. 内存使用

- 路径map大小设置为2048条目
- 每个条目占用约16字节
- 总内存使用约32KB

### 3. 查找性能

- BPF map查找时间复杂度为O(1)
- 路径哈希计算开销较小
- 整体性能影响可忽略

## 限制和注意事项

### 1. 路径长度限制

- Linux PATH_MAX通常为4096字节
- BPF程序中建议限制为256字节
- 超长路径可能被截断

### 2. 符号链接

- 路径解析可能受符号链接影响
- 建议使用规范化路径
- 考虑链接目标的安全性

### 3. 内核兼容性

- 某些路径获取方法需要较新内核
- 建议使用用户空间缓存方案
- 测试不同内核版本的兼容性

## 最佳实践

### 1. 配置策略

- 同时使用名称和路径控制
- 路径白名单应尽可能具体
- 定期审查和更新路径列表

### 2. 监控和调试

- 记录被拒绝的路径访问
- 监控哈希冲突情况
- 定期检查路径有效性

### 3. 安全考虑

- 使用绝对路径，避免相对路径
- 考虑chroot和容器环境
- 定期更新可信路径列表

## 故障排除

### 1. 路径不匹配

**问题**：配置的路径与实际路径不匹配
**解决**：使用 `readlink -f /proc/PID/exe` 查看实际路径

### 2. 权限问题

**问题**：无法读取某些进程的路径信息
**解决**：确保程序以root权限运行

### 3. 性能问题

**问题**：路径检查影响性能
**解决**：优化哈希函数，减少map大小

## 总结

进程绝对路径控制提供了比传统进程名控制更高的安全性和精确性。通过检查进程的完整可执行文件路径，可以有效防止进程名欺骗攻击，提供更细粒度的访问控制。

建议在安全要求较高的环境中使用路径控制，并与传统名称控制结合使用，以获得最佳的兼容性和安全性。
