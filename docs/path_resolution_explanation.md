# 策略配置路径 vs 运行时路径获取

## 你的疑问

> "策略加载不就是用的绝对路径吗，为什么还要通过 /proc/PID/exe 获取真实路径？"

这是一个很好的问题！让我解释这两个不同阶段的作用。

## 两个不同的阶段

### 阶段1：策略配置（静态）
**作用**：定义允许/拒绝的路径规则
**时机**：程序启动时，加载配置文件
**内容**：管理员预先配置的绝对路径

```yaml
# config/policy.yaml
process_path_allow_list:
  - "/usr/bin/curl"      # 管理员配置：允许系统curl
  - "/usr/bin/wget"      # 管理员配置：允许系统wget
  
process_path_deny_list:
  - "/tmp/curl"          # 管理员配置：拒绝临时目录的curl
  - "/var/tmp/malware"   # 管理员配置：拒绝恶意软件
```

**处理过程**：
```python
# 1. 读取配置文件中的路径
config_paths = ["/usr/bin/curl", "/usr/bin/wget", "/tmp/curl"]

# 2. 计算每个配置路径的哈希
for path in config_paths:
    path_hash = hash_long_string(path)
    bpf_key = (policy_id << 32) | path_hash
    # 3. 将哈希存入BPF Map
    process_path_allow_map[bpf_key] = 1
```

### 阶段2：运行时检查（动态）
**作用**：获取实际运行进程的真实路径
**时机**：进程尝试网络连接时
**内容**：系统中实际运行的进程路径

```python
# 当进程PID=1234尝试网络连接时
pid = 1234
# 通过 /proc/PID/exe 获取这个进程的真实路径
actual_path = os.readlink(f"/proc/{pid}/exe")  # -> "/usr/bin/curl"
```

## 为什么需要运行时获取路径？

### 1. **BPF程序无法直接获取路径字符串**

BPF程序运行在内核态，有严格限制：
- 不能进行复杂的字符串操作
- 不能直接读取 `/proc/PID/exe`
- 只能进行简单的数值比较

```c
// BPF程序中无法这样做：
char actual_path[256];
readlink("/proc/1234/exe", actual_path, 256);  // ❌ 不支持

// 只能这样做：
u32 pid = bpf_get_current_pid_tgid() >> 32;
u64 *cached_hash = pid_path_cache.lookup(&pid);  // ✅ 支持
```

### 2. **需要将实际路径转换为哈希进行比较**

```python
# 运行时流程：
pid = 1234
actual_path = "/usr/bin/curl"           # 通过 /proc/PID/exe 获取
actual_hash = hash_long_string(actual_path)  # 计算哈希: 2127771067

# 更新缓存供BPF程序使用
pid_path_cache[pid] = actual_hash
```

```c
// BPF程序中的匹配：
u32 pid = bpf_get_current_pid_tgid() >> 32;
u64 *actual_hash = pid_path_cache.lookup(&pid);  // 获取实际路径哈希
u64 bpf_key = (policy_id << 32) | (*actual_hash);
u8 *allowed = process_path_allow_map.lookup(&bpf_key);  // 检查是否允许
```

## 完整匹配流程示例

### 场景：curl进程访问端口80

**1. 策略配置阶段**：
```yaml
# 管理员配置
process_path_allow_list:
  - "/usr/bin/curl"  # 哈希: 2127771067
```

**2. 策略加载**：
```python
config_hash = hash_long_string("/usr/bin/curl")  # 2127771067
bpf_key = (1 << 32) | 2127771067  # 6422738363
process_path_allow_map[6422738363] = 1  # 存入BPF Map
```

**3. 运行时检查**：
```python
# 进程PID=1234尝试连接
pid = 1234
actual_path = os.readlink("/proc/1234/exe")  # "/usr/bin/curl"
actual_hash = hash_long_string(actual_path)  # 2127771067
pid_path_cache[1234] = 2127771067  # 更新缓存
```

**4. BPF程序匹配**：
```c
pid = 1234;
actual_hash = pid_path_cache.lookup(&pid);  // 2127771067
bpf_key = (1 << 32) | 2127771067;  // 6422738363
allowed = process_path_allow_map.lookup(&bpf_key);  // 找到匹配！
return 1;  // 允许连接
```

## 关键差异对比

| 方面 | 策略配置路径 | 运行时路径获取 |
|------|-------------|---------------|
| **来源** | 配置文件 | `/proc/PID/exe` |
| **时机** | 程序启动时 | 网络连接时 |
| **目的** | 定义规则 | 获取实际情况 |
| **处理** | 计算哈希存入Map | 获取路径计算哈希 |
| **环境** | 用户空间 | 用户空间 |

## 为什么不能省略运行时获取？

### 1. **BPF程序无法直接获取路径**
```c
// BPF程序中无法这样做：
char path[256];
get_process_executable_path(path);  // ❌ 不存在这样的函数
```

### 2. **需要实时获取当前进程信息**
```python
# 每个网络连接都可能来自不同的进程
connection_1: PID=1234 -> "/usr/bin/curl"
connection_2: PID=5678 -> "/tmp/malicious_curl"
connection_3: PID=9999 -> "/usr/bin/wget"
```

### 3. **哈希匹配需要两个哈希值**
```
配置哈希（预计算）: hash("/usr/bin/curl") = 2127771067
实际哈希（运行时）: hash(readlink("/proc/1234/exe")) = 2127771067
匹配结果: 2127771067 == 2127771067 ✅ 允许
```

## 总结

**策略配置路径**：
- 管理员预先定义的规则
- 在程序启动时处理
- 计算哈希存入BPF Map

**运行时路径获取**：
- 获取实际运行进程的真实路径
- 在网络连接时处理  
- 计算哈希与配置哈希比较

**两者缺一不可**：
- 没有配置路径 → 不知道允许/拒绝什么
- 没有运行时获取 → 不知道当前进程是什么

这就像是"规则"和"事实"的对比：
- **配置路径** = 规则："我允许 /usr/bin/curl"
- **运行时路径** = 事实："当前进程是 /usr/bin/curl"
- **匹配过程** = 判断："事实是否符合规则"
