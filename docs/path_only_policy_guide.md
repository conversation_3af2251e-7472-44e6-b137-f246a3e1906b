# 进程路径专用网络策略控制指南

## 概述

这是一个专门针对进程路径管控的eBPF网络策略控制系统，**不包含进程名管控功能**。系统基于进程的完整路径来决定是否允许网络连接，提供更精确的安全控制。

## 核心特性

### ✅ 支持的功能

1. **进程路径白名单** - 只允许指定路径的程序进行网络连接
2. **进程路径黑名单** - 明确拒绝指定路径的程序进行网络连接
3. **IP地址控制** - 支持IP白名单和黑名单
4. **端口级别策略** - 不同端口可以配置不同的策略
5. **实时统计** - 提供连接统计和策略执行统计

### ❌ 不支持的功能

1. **进程名管控** - 不基于进程名进行控制
2. **用户级别控制** - 不基于用户ID进行控制
3. **时间控制** - 不支持基于时间的策略

## 文件结构

```
src/bpf_network_policy/bpf/bpf_path_only_control.c     # BPF内核程序
src/bpf_network_policy/core/path_only_policy_control.py # Python控制程序
config/path_only_policy.yaml                           # 策略配置文件
examples/path_only_demo.py                            # 使用演示
```

## BPF程序架构

### 数据结构

```c
// 端口策略映射 - 每个端口对应一个策略ID
BPF_HASH(port_policy_map, u16, u32, 256);

// 进程路径白名单 - key: policy_id << 32 | process_path_hash, value: 1
BPF_HASH(process_path_allow_map, u64, u8, 2048);

// 进程路径黑名单 - key: policy_id << 32 | process_path_hash, value: 1
BPF_HASH(process_path_deny_map, u64, u8, 2048);

// IP白名单 - key: policy_id << 32 | ip_addr, value: 1
BPF_HASH(cidr_allow_map, u64, u8, 1024);

// IP黑名单 - key: policy_id << 32 | ip_addr, value: 1
BPF_HASH(cidr_deny_map, u64, u8, 1024);

// 统计信息
BPF_HASH(policy_stats, u32, u64, 10);
```

### 决策逻辑

1. **端口查找** - 根据目标端口查找对应的策略ID
2. **路径黑名单检查** - 如果进程路径在黑名单中，直接拒绝
3. **IP黑名单检查** - 如果目标IP在黑名单中，直接拒绝
4. **白名单验证** - 进程路径必须在白名单中 AND 目标IP必须在白名单中
5. **统计更新** - 更新相应的统计计数器

## 配置文件格式

### 基本结构

```yaml
ports:
  - ports: ["80", "443"]                    # 端口列表
    process_path:                           # 进程路径配置
      process_path_allow_list:              # 路径白名单
        - "/usr/bin/curl"
        - "/usr/bin/wget"
        - "/usr/bin/python3"
      process_path_deny_list:               # 路径黑名单
        - "/usr/bin/nc"
        - "/tmp/malicious"
    cidr:                                   # IP配置
      cidr_allow_list:                      # IP白名单
        - "0.0.0.0/0"
      cidr_deny_list:                       # IP黑名单
        - "*************/24"
```

### 配置示例

#### HTTP/HTTPS 策略
```yaml
- ports: ["80", "443"]
  process_path:
    process_path_allow_list:
      - "/usr/bin/curl"
      - "/usr/bin/wget"
      - "/usr/bin/firefox"
      - "/opt/google/chrome/chrome"
    process_path_deny_list:
      - "/usr/bin/nc"
      - "/usr/bin/telnet"
  cidr:
    cidr_allow_list:
      - "0.0.0.0/0"
    cidr_deny_list: []
```

#### 数据库策略
```yaml
- ports: ["3306"]
  process_path:
    process_path_allow_list:
      - "/usr/bin/mysql"
      - "/usr/bin/python3"
      - "/opt/myapp/bin/myapp"
    process_path_deny_list:
      - "/usr/bin/nc"
      - "/tmp/exploit"
  cidr:
    cidr_allow_list:
      - "127.0.0.1/32"
      - "***********/24"
    cidr_deny_list:
      - "0.0.0.0/0"
```

## 使用方法

### 1. 基本使用

```bash
# 使用默认配置文件
sudo python3 -m src.bpf_network_policy.core.path_only_policy_control

# 使用自定义配置文件
sudo python3 -m src.bpf_network_policy.core.path_only_policy_control my_policy.yaml
```

### 2. 编程接口

```python
from src.bpf_network_policy.core.path_only_policy_control import PathOnlyPolicyController

# 创建控制器
controller = PathOnlyPolicyController("config/path_only_policy.yaml")

# 加载策略
controller.load_policy()

# 加载BPF程序
controller.load_bpf_program()

# 配置策略
controller.configure_policies()

# 显示统计
controller.show_statistics()
```

### 3. 运行演示

```bash
sudo python3 examples/path_only_demo.py
```

## 测试验证

### 编译测试
```bash
sudo python3 test_path_only_bpf.py
```

### 测试结果示例
```
进程路径专用BPF代码测试
==================================================
✅ BPF程序编译成功
✅ 成功获取所有BPF表
✅ 添加端口策略
✅ 添加路径白名单: /usr/bin/curl
✅ 添加路径白名单: /usr/bin/wget
✅ 添加路径白名单: /usr/bin/python3
✅ 添加路径黑名单: /usr/bin/nc
✅ 路径策略数量: 4
```

## 路径哈希算法

系统使用增强的哈希算法来处理长路径：

```c
static inline u32 hash_long_string(char *str, int max_len) {
    u32 hash = 5381;
    for (int i = 0; i < max_len && str[i] != 0; i++) {
        hash = ((hash << 5) + hash) + str[i];
    }
    return hash;
}
```

### 哈希示例
```
/usr/bin/curl                    -> 0x7ed335bb (2127771067)
/usr/bin/python3                 -> 0x81973e3a (2174172730)
/opt/google/chrome/chrome        -> 0xb1036dad (2969791917)
```

## 性能特点

### 优势
- **高效查找** - 使用BPF哈希表，O(1)查找复杂度
- **内存优化** - 只存储路径哈希值，不存储完整路径
- **低延迟** - 在内核空间直接处理，避免用户空间切换

### 限制
- **路径长度** - 支持最大256字符的路径
- **哈希冲突** - 极少情况下可能出现哈希冲突
- **路径获取** - 当前版本使用进程名作为路径的后备方案

## 部署建议

### 1. 开发环境
- 使用宽松的白名单策略
- 启用详细的统计和日志
- 定期检查拒绝的连接

### 2. 生产环境
- 使用严格的白名单策略
- 定期更新路径白名单
- 监控统计数据异常

### 3. 安全考虑
- 定期审查路径白名单
- 监控新的可执行文件
- 防止路径欺骗攻击

## 故障排除

### 常见问题

1. **编译失败**
   - 检查BCC是否正确安装
   - 确认内核版本支持eBPF
   - 验证root权限

2. **策略不生效**
   - 检查BPF程序是否正确附加
   - 验证cgroup配置
   - 确认路径哈希计算正确

3. **性能问题**
   - 减少策略数量
   - 优化路径匹配规则
   - 监控内存使用

### 调试方法

1. **启用详细日志**
2. **检查统计信息**
3. **验证哈希计算**
4. **测试单个策略**

## 扩展开发

### 添加新功能
1. 修改BPF程序添加新的数据结构
2. 更新Python控制程序
3. 扩展配置文件格式
4. 添加相应的测试

### 性能优化
1. 优化哈希算法
2. 减少内存使用
3. 改进查找效率
4. 批量操作支持

## 总结

这个进程路径专用网络策略控制系统提供了：

- ✅ **专注性** - 专门针对进程路径管控
- ✅ **高效性** - 基于eBPF的高性能实现
- ✅ **灵活性** - 支持复杂的策略配置
- ✅ **可靠性** - 经过全面测试验证

适用于需要精确控制哪些程序可以进行网络连接的安全场景。
