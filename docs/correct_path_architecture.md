# 正确的绝对路径匹配架构

## 你的质疑是对的！

> "不对吧，你运行时获取进程绝对路径那应该是bpf侧该做的啊"

**你说得完全正确！** 我之前的架构设计有问题。

## 错误的架构（之前的实现）

### ❌ 错误方式：依赖用户空间缓存

```c
// 错误：BPF程序依赖用户空间维护的缓存
u64 *cached_path_hash = pid_path_cache.lookup(&pid);
u32 path_hash = 0;
if (cached_path_hash) {
    path_hash = (u32)(*cached_path_hash);
}
```

**问题**：
1. **架构混乱**：BPF程序不应该依赖用户空间的实时更新
2. **性能问题**：需要用户空间不断扫描 `/proc` 更新缓存
3. **可靠性差**：缓存可能不及时或不准确
4. **复杂度高**：需要维护PID到路径的映射关系

## 正确的架构（修正后）

### ✅ 正确方式：BPF程序直接获取路径

```c
// 正确：BPF程序直接获取当前进程路径
static inline int get_process_path(char *path_buf, int buf_size) {
    struct task_struct *task = (struct task_struct *)bpf_get_current_task();
    if (!task) return -1;
    
    struct mm_struct *mm = task->mm;
    if (!mm) return -1;
    
    struct file *exe_file = mm->exe_file;
    if (!exe_file) return -1;
    
    // 直接从内核获取路径
    return bpf_d_path(&exe_file->f_path, path_buf, buf_size);
}

int check_connection_policy_with_path(struct bpf_sock_addr *ctx) {
    // 直接在BPF程序中获取进程路径
    char proc_path[256];
    u32 path_hash = 0;
    int path_ret = get_process_path(proc_path, sizeof(proc_path));
    if (path_ret >= 0) {
        path_hash = hash_long_string(proc_path, sizeof(proc_path));
    }
    
    // 直接进行匹配
    u64 bpf_key = ((u64)policy_id << 32) | path_hash;
    u8 *allowed = process_path_allow_map.lookup(&bpf_key);
    // ...
}
```

## 正确的职责分工

### **用户空间（User Space）**：
```python
# 只负责策略配置
def load_policy_config():
    config_paths = ["/usr/bin/curl", "/usr/bin/wget", "/tmp/malware"]
    
    for path in config_paths:
        # 计算配置路径的哈希
        path_hash = hash_long_string(path)
        bpf_key = (policy_id << 32) | path_hash
        # 存入BPF Map
        process_path_allow_map[bpf_key] = 1
```

**职责**：
- ✅ 读取配置文件
- ✅ 计算配置路径哈希
- ✅ 初始化BPF Map
- ❌ ~~获取运行时进程路径~~
- ❌ ~~维护PID缓存~~

### **BPF程序（Kernel Space）**：
```c
int check_connection_policy_with_path(struct bpf_sock_addr *ctx) {
    // 1. 直接获取当前进程路径
    char proc_path[256];
    get_process_path(proc_path, sizeof(proc_path));
    
    // 2. 计算路径哈希
    u32 path_hash = hash_long_string(proc_path, sizeof(proc_path));
    
    // 3. 构造查找键
    u64 bpf_key = ((u64)policy_id << 32) | path_hash;
    
    // 4. 直接匹配
    u8 *allowed = process_path_allow_map.lookup(&bpf_key);
    return allowed ? 1 : 0;
}
```

**职责**：
- ✅ 获取当前进程路径
- ✅ 计算实时路径哈希
- ✅ 执行策略匹配
- ✅ 返回允许/拒绝决策

## 架构对比

| 方面 | 错误架构 | 正确架构 |
|------|----------|----------|
| **路径获取** | 用户空间扫描 `/proc` | BPF程序直接获取 |
| **缓存维护** | 需要PID缓存 | 无需缓存 |
| **实时性** | 依赖缓存更新频率 | 实时获取 |
| **复杂度** | 高（需要同步机制） | 低（直接获取） |
| **可靠性** | 缓存可能过期 | 总是最新 |
| **性能** | 用户空间开销大 | 内核态高效 |

## 为什么之前的理解是错误的？

### 1. **混淆了配置时和运行时**
- **配置时**：用户空间处理配置文件中的路径 ✅
- **运行时**：应该由BPF程序获取当前进程路径 ✅

### 2. **过度设计了缓存机制**
```python
# 不需要这样做：
def update_pid_cache():
    for pid in get_all_pids():
        path = os.readlink(f"/proc/{pid}/exe")
        path_hash = hash_long_string(path)
        pid_path_cache[pid] = path_hash  # ❌ 不必要
```

### 3. **忽略了BPF的能力**
BPF程序完全有能力直接获取进程信息：
```c
// BPF程序可以直接做这些：
bpf_get_current_task()      // 获取当前任务
bpf_get_current_pid_tgid()  // 获取PID
bpf_get_current_comm()      // 获取进程名
bpf_d_path()               // 获取文件路径 ✅
```

## 修正后的完整流程

### 1. **程序启动时（用户空间）**：
```python
# 加载策略配置
config = load_yaml_config("policy.yaml")
for path in config["process_path_allow_list"]:
    path_hash = hash_long_string(path)
    bpf_key = (policy_id << 32) | path_hash
    process_path_allow_map[bpf_key] = 1
```

### 2. **网络连接时（BPF程序）**：
```c
// 直接获取当前进程路径
char proc_path[256];
get_process_path(proc_path, sizeof(proc_path));  // "/usr/bin/curl"

// 计算哈希
u32 path_hash = hash_long_string(proc_path, 256);  // 2127771067

// 匹配策略
u64 bpf_key = (policy_id << 32) | path_hash;  // 6422738363
u8 *allowed = process_path_allow_map.lookup(&bpf_key);
return allowed ? 1 : 0;
```

## 技术优势

### ✅ **正确架构的优势**：
1. **简洁性**：无需维护复杂的缓存机制
2. **实时性**：总是获取最新的进程信息
3. **可靠性**：直接从内核获取，无中间环节
4. **高效性**：避免用户空间的频繁扫描
5. **架构清晰**：职责分工明确

### ❌ **错误架构的问题**：
1. **复杂性**：需要同步用户空间和内核空间
2. **延迟性**：缓存更新可能不及时
3. **资源浪费**：用户空间需要不断扫描 `/proc`
4. **架构混乱**：BPF程序依赖用户空间状态

## 总结

**你的质疑完全正确！** 

运行时获取进程绝对路径确实应该是BPF侧的职责，而不是用户空间维护缓存。正确的架构应该是：

- **用户空间**：只负责策略配置和BPF程序加载
- **BPF程序**：负责实时获取进程路径并执行匹配

这样的设计更加简洁、高效、可靠。感谢你的纠正！
