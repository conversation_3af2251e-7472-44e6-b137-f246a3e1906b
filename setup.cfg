[metadata]
name = bpf-network-policy
version = 1.0.0
author = BPF Policy Team
author_email = <EMAIL>
description = eBPF-based Network Policy Control System
long_description = file: docs/README.md
long_description_content_type = text/markdown
url = https://github.com/example/bpf-network-policy
project_urls =
    Bug Reports = https://github.com/example/bpf-network-policy/issues
    Source = https://github.com/example/bpf-network-policy
    Documentation = https://bpf-network-policy.readthedocs.io/
classifiers =
    Development Status :: 4 - Beta
    Intended Audience :: System Administrators
    Intended Audience :: Developers
    License :: OSI Approved :: MIT License
    Operating System :: POSIX :: Linux
    Programming Language :: Python :: 3
    Programming Language :: Python :: 3.7
    Programming Language :: Python :: 3.8
    Programming Language :: Python :: 3.9
    Programming Language :: Python :: 3.10
    Programming Language :: Python :: 3.11
    Programming Language :: C
    Topic :: System :: Networking :: Firewalls
    Topic :: System :: Systems Administration
    Topic :: Security

[options]
packages = find:
package_dir =
    = src
python_requires = >=3.7
install_requires =
    PyYAML>=6.0,<7.0

[options.packages.find]
where = src

[options.extras_require]
dev =
    pytest>=7.0.0
    pytest-cov>=4.0.0
    pytest-mock>=3.10.0
    black>=23.0.0
    flake8>=6.0.0
    mypy>=1.0.0
    isort>=5.12.0
docs =
    sphinx>=6.0.0
    sphinx-rtd-theme>=1.2.0
monitoring =
    structlog>=23.0.0
    colorlog>=6.7.0

[options.entry_points]
console_scripts =
    bpf-policy-control = bpf_network_policy.core.policy_control:main
    bpf-legacy-control = bpf_network_policy.core.user_control:main
    bpf-policy-test = tests.integration.test_yaml_simple:main
    bpf-intercept-test = tests.integration.test_basic_intercept:main

[options.package_data]
bpf_network_policy.bpf = *.c, *.h
config = *.yaml, *.yml
config.examples = *.yaml, *.yml

# Tool configurations
[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = 
    --verbose
    --tb=short
    --strict-markers
    --disable-warnings
markers =
    unit: Unit tests
    integration: Integration tests
    requires_root: Tests requiring root privileges
    requires_bcc: Tests requiring BCC framework
    slow: Slow running tests

[coverage:run]
source = src/bpf_network_policy
omit = 
    */tests/*
    */test_*
    setup.py

[coverage:report]
exclude_lines =
    pragma: no cover
    def __repr__
    raise AssertionError
    raise NotImplementedError
    if __name__ == .__main__.:

[flake8]
max-line-length = 88
extend-ignore = E203, W503
exclude = 
    .git,
    __pycache__,
    build,
    dist,
    *.egg-info

[mypy]
python_version = 3.7
warn_return_any = True
warn_unused_configs = True
disallow_untyped_defs = True
disallow_incomplete_defs = True
check_untyped_defs = True
disallow_untyped_decorators = True
no_implicit_optional = True
warn_redundant_casts = True
warn_unused_ignores = True
warn_no_return = True
warn_unreachable = True
strict_equality = True

[isort]
profile = black
multi_line_output = 3
line_length = 88
known_first_party = bpf_network_policy
known_third_party = bcc,yaml,pytest
