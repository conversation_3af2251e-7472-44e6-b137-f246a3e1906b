#!/usr/bin/env python3
"""
Setup script for BPF Network Policy Control System

This setup script provides installation and packaging configuration
for the BPF network policy control system.
"""

from setuptools import setup, find_packages
import os
import sys

# Ensure Python 3.7+
if sys.version_info < (3, 7):
    sys.exit("Python 3.7 or higher is required")

# Read long description from README
def read_readme():
    """Read README.md for long description."""
    readme_path = os.path.join(os.path.dirname(__file__), 'README.md')
    if os.path.exists(readme_path):
        with open(readme_path, 'r', encoding='utf-8') as f:
            return f.read()
    return "BPF Network Policy Control System"

# Read requirements from requirements.txt
def read_requirements():
    """Read requirements from requirements.txt."""
    req_path = os.path.join(os.path.dirname(__file__), 'requirements.txt')
    requirements = []
    if os.path.exists(req_path):
        with open(req_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                # Skip comments and empty lines
                if line and not line.startswith('#'):
                    # Skip system-level dependencies
                    if not any(skip in line.lower() for skip in ['bcc', 'kernel', 'llvm', 'clang']):
                        requirements.append(line)
    return requirements

setup(
    name="bpf-network-policy",
    version="1.0.0",
    description="eBPF-based Network Policy Control System",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    author="BPF Policy Team",
    author_email="<EMAIL>",
    url="https://github.com/example/bpf-network-policy",
    
    # Package configuration
    packages=find_packages(where="src"),
    package_dir={"": "src"},
    
    # Python version requirement
    python_requires=">=3.7",
    
    # Dependencies
    install_requires=read_requirements(),
    
    # Optional dependencies
    extras_require={
        'dev': [
            'pytest>=7.0.0',
            'pytest-cov>=4.0.0',
            'pytest-mock>=3.10.0',
            'black>=23.0.0',
            'flake8>=6.0.0',
            'mypy>=1.0.0',
            'isort>=5.12.0',
        ],
        'docs': [
            'sphinx>=6.0.0',
            'sphinx-rtd-theme>=1.2.0',
        ],
        'monitoring': [
            'structlog>=23.0.0',
            'colorlog>=6.7.0',
        ],
    },
    
    # Entry points for command-line scripts
    entry_points={
        'console_scripts': [
            'bpf-policy-control=bpf_network_policy.core.policy_control:main',
            'bpf-legacy-control=bpf_network_policy.core.user_control:main',
            'bpf-policy-test=tests.integration.test_yaml_simple:main',
            'bpf-intercept-test=tests.integration.test_basic_intercept:main',
        ],
    },
    
    # Package data
    package_data={
        'bpf_network_policy.bpf': ['*.c', '*.h'],
        'config': ['*.yaml', '*.yml'],
        'config.examples': ['*.yaml', '*.yml'],
    },
    
    # Include additional files
    include_package_data=True,
    
    # Classifiers
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: System Administrators",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: POSIX :: Linux",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.7",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: C",
        "Topic :: System :: Networking :: Firewalls",
        "Topic :: System :: Systems Administration",
        "Topic :: Security",
    ],
    
    # Keywords
    keywords="bpf ebpf network policy security firewall linux kernel",
    
    # Project URLs
    project_urls={
        "Bug Reports": "https://github.com/example/bpf-network-policy/issues",
        "Source": "https://github.com/example/bpf-network-policy",
        "Documentation": "https://bpf-network-policy.readthedocs.io/",
    },
    
    # Zip safe
    zip_safe=False,
)
